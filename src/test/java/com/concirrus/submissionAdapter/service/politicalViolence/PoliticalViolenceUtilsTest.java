package com.concirrus.submissionAdapter.service.politicalViolence;

import com.concirrus.submissionAdapter.dal.AccountDal;
import com.concirrus.submissionAdapter.dal.AccountRepository;
import com.concirrus.submissionAdapter.dal.LocationRepository;
import com.concirrus.submissionAdapter.dto.currencyExchange.LocationUpdateResult;
import com.concirrus.submissionAdapter.dto.enums.State;
import com.concirrus.submissionAdapter.dto.enums.UpdateType;
import com.concirrus.submissionAdapter.dto.submission.InsuredAssetsResponse;
import com.concirrus.submissionAdapter.dto.submission.Quote;
import com.concirrus.submissionAdapter.dto.submission.Submission;
import com.concirrus.submissionAdapter.model.Account;
import com.concirrus.submissionAdapter.sal.SubmissionHandlerSal;
import com.concirrus.submissionAdapter.utils.CurrencyConverter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
@ExtendWith(MockitoExtension.class)
@DisplayName("PoliticalViolenceUtils Tests")
class PoliticalViolenceUtilsTest {
    @Mock
    private CurrencyConverter currencyConverter;
    @Mock private SubmissionHandlerSal submissionHandlerSal;
    @Mock private AccountRepository accountRepository;
    @Mock private LocationRepository locationRepository;
    @Mock private AccountDal accountDal;

    @InjectMocks
    private PoliticalViolenceUtils utils;

    @Test
    void testIngestLocations_shouldReturnTotalCount() {
        // Arrange
        String submissionId = "sub123";
        String clientId = "client123";
        State state = State.IN_REVIEW;
        int page = 0;

        InsuredAssetsResponse response = mock(InsuredAssetsResponse.class);
        InsuredAssetsResponse.Result result = new InsuredAssetsResponse.Result();
        result.setLast(true);
        result.setContent(List.of(Map.of("id", "loc1", "latitude", 1.0, "longitude", 2.0)));

        when(response.getStatus()).thenReturn(200);
        when(response.getResult()).thenReturn(result);
        when(submissionHandlerSal.getInsuredAssetsForSubmission(submissionId, "POLITICAL_VIOLENCE", page, 50, clientId))
                .thenReturn(response);

        when(locationRepository.saveAll(any())).thenReturn(Collections.emptyList());

        // Act
        int count = utils.ingestLocations(submissionId, clientId, state, UpdateType.LOCATION_CREATED);

        // Assert
        assertEquals(1, count);
        verify(locationRepository).saveAll(any());
    }

    @Test
    void testIngestQuote_shouldSaveAccount() {
        // Given
        Submission submission = new Submission();
        submission.setSubmissionId("sub123");
        submission.setClientId("client1");
        submission.setLineOfBusiness("POLITICAL_VIOLENCE");
        submission.setState(State.BOUND);

        Quote quote = new Quote();
        quote.setQuoteId("quote123");

        // Setup riskDetails map with all required fields
        Map<String, Object> riskDetails = new HashMap<>();
        riskDetails.put("pdDeductible", 100.0);
        riskDetails.put("excess", 200.0);
        riskDetails.put("limits", 1000.0);  // Note: "limits" not "limit"
        riskDetails.put("policyCurrency", "USD");
        riskDetails.put("inceptionDate", "2024-01-01");
        riskDetails.put("expiryDate", "2024-12-31");
        riskDetails.put("peril", Arrays.asList("War", "SRCC"));
        quote.setRiskDetails(riskDetails);

        // Setup referencing map for line values and premium
        Map<String, Object> referencing = new HashMap<>();
        referencing.put("writtenLine", 50.0);
        referencing.put("signedLine", 45.0);
        referencing.put("policyReference", "POL123");
        referencing.put("appliedCrisisNetPremiumUsd", 5000.0);
        quote.setReferencing(referencing);

        // Setup mocks
        Account expectedAccount = new Account();
        expectedAccount.setQuoteId("quote123");
        expectedAccount.setSubmissionId("sub123");
        expectedAccount.setDeductible(100.0);
        expectedAccount.setExcess(200.0);
        expectedAccount.setLimit(1000.0);

        when(accountRepository.findBySubmissionIdAndQuoteId("sub123", "quote123"))
                .thenReturn(Optional.empty());
        when(currencyConverter.getExchangeRate("USD", "client1")).thenReturn(1.0);
        when(accountDal.createOrUpdateAccount(any(Account.class))).thenReturn(expectedAccount);

        // When
        Account result = utils.ingestQuote(submission, quote);

        // Then
        assertNotNull(result);
        assertEquals("quote123", result.getQuoteId());
        assertEquals("sub123", result.getSubmissionId());

        // Verify the account was processed through accountDal, not directly saved to repository
        verify(accountDal).createOrUpdateAccount(any(Account.class));
        verify(accountRepository).findBySubmissionIdAndQuoteId("sub123", "quote123");
        verify(currencyConverter).getExchangeRate("USD", "client1");

        // Verify accountRepository.save() is NOT called directly (it's handled by accountDal)
        verify(accountRepository, never()).save(any());
    }

    @Test
    void testHaveRelevantFieldsChangedForQuote_shouldDetectChange() {
        Quote quote = new Quote();
        Map<String, Object> riskDetails = new HashMap<>();
        riskDetails.put("pdDeductible", 200.0);
        riskDetails.put("excess", 200.0);
        riskDetails.put("limit", 200.0);
        riskDetails.put("policyCurrency", "USD");
        quote.setRiskDetails(riskDetails);
        Submission submission = new Submission();
        submission.setClientId("client1");
        submission.setState(State.valueOf("BOUND"));

        Account account = new Account();
        account.setDeductible(100.0);
        account.setExcess(100.0);
        account.setLimit(100.0);
        account.setWrittenLine(10.0);
        account.setPolicyCurrency("USD");

        when(currencyConverter.getExchangeRate(anyString(), anyString())).thenReturn(1.0);
        boolean changed = utils.haveRelevantFieldsChangedForQuote(account, quote, submission);
        assertTrue(changed);
    }

    @Test
    void testProcessAllLocationPages_shouldProcessMultiplePages() {
        String submissionId = "sub1";
        String clientId = "client1";

        InsuredAssetsResponse.Result result1 = new InsuredAssetsResponse.Result();
        result1.setLast(false);
        result1.setContent(List.of(Map.of("id", "loc1")));

        InsuredAssetsResponse.Result result2 = new InsuredAssetsResponse.Result();
        result2.setLast(true);
        result2.setContent(List.of(Map.of("id", "loc2")));

        InsuredAssetsResponse response1 = new InsuredAssetsResponse();
        response1.setResult(result1);
        response1.setStatus(200);

        InsuredAssetsResponse response2 = new InsuredAssetsResponse();
        response2.setResult(result2);
        response2.setStatus(200);

        when(submissionHandlerSal.getInsuredAssetsForSubmission(eq(submissionId), anyString(), eq(0), anyInt(), eq(clientId))).thenReturn(response1);
        when(submissionHandlerSal.getInsuredAssetsForSubmission(eq(submissionId), anyString(), eq(1), anyInt(), eq(clientId))).thenReturn(response2);

        when(locationRepository.findAllById(any())).thenReturn(Collections.emptyList());
        when(locationRepository.saveAll(any())).thenReturn(Collections.emptyList());

        LocationUpdateResult result = utils.processAllLocationPages(submissionId, clientId);

        assertNotNull(result);
        assertEquals(2, result.getCoordinateUpdatedLocations().size());
    }

}