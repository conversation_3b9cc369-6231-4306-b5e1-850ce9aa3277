package com.concirrus.submissionAdapter.service.politicalViolence;

import com.concirrus.submissionAdapter.dal.AggregationJobRepository;
import com.concirrus.submissionAdapter.dal.LocationTaskRepository;
import com.concirrus.submissionAdapter.dto.enums.ProcessingStatus;
import com.concirrus.submissionAdapter.model.AggregationJob;
import com.concirrus.submissionAdapter.utils.SlackNotifier;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("CleanUpService Tests")
class CleanUpServiceTest {

    private CleanUpService cleanUpService;

    private SlackNotifier slackNotifier;

    private AggregationJobRepository aggregationJobRepository;

    private LocationTaskRepository locationTaskRepository;

    @BeforeEach
    void setUp() {
        //MockitoAnnotations.openMocks(this);
        // Set cleanup threshold to 30 days
        slackNotifier = mock(SlackNotifier.class);
        aggregationJobRepository = mock(AggregationJobRepository.class);
        locationTaskRepository = mock(LocationTaskRepository.class);

        cleanUpService= new CleanUpService(slackNotifier, aggregationJobRepository, locationTaskRepository);
        ReflectionTestUtils.setField(cleanUpService, "cleanUpThreshold", 30L);

    }

    @Test
    void testCleanUpOldJobs_shouldDeleteOldJobsAndSendNotification() {
        AggregationJob job1 = AggregationJob.builder().jobId("job-1").build();
        AggregationJob job2 = AggregationJob.builder().jobId("job-2").build();
        List<AggregationJob> jobs = Arrays.asList(job1, job2);

        when(aggregationJobRepository.findByStatusAndUpdatedAtBefore(eq(ProcessingStatus.COMPLETED), any()))
                .thenReturn(jobs);

        cleanUpService.cleanUp();

        verify(slackNotifier).sendAlert(contains("Starting clean up job"));
        verify(locationTaskRepository, times(2)).deleteByJobId(anyString());
        verify(aggregationJobRepository).deleteAll(jobs);
        verify(slackNotifier).sendAlert(contains("Clean up job completed"));
    }

    @Test
    void testCleanUpOldJobs_noOldJobsFound() {
        when(aggregationJobRepository.findByStatusAndUpdatedAtBefore(eq(ProcessingStatus.COMPLETED), any()))
                .thenReturn(Collections.emptyList());

        cleanUpService.cleanUp();

        verify(locationTaskRepository, never()).deleteByJobId(anyString());
        verify(aggregationJobRepository, never()).deleteAll(any());
        verify(slackNotifier).sendAlert(contains("Starting clean up job"));
        verify(slackNotifier).sendAlert(contains("Clean up job completed"));
    }

    @Test
    void testCleanUpOldJobs_whenExceptionOccurs_shouldLogAndSendSlackError() {
        when(aggregationJobRepository.findByStatusAndUpdatedAtBefore(any(), any()))
                .thenThrow(new RuntimeException("DB error"));

        assertThrows(RuntimeException.class, () -> cleanUpService.cleanUp());

        verify(slackNotifier).sendAlert(contains("Starting clean up job"));
        verify(slackNotifier).sendAlert(contains("Error occurred while cleaning up old jobs: DB error"));
    }
}
