package com.concirrus.submissionAdapter.service.politicalViolence;

import com.concirrus.submissionAdapter.dal.AccountDal;
import com.concirrus.submissionAdapter.dal.AccountRepository;
import com.concirrus.submissionAdapter.dal.LocationRepository;
import com.concirrus.submissionAdapter.model.Account;
import com.concirrus.submissionAdapter.model.Location;
import com.concirrus.submissionAdapter.sal.BlastZoneProcessorSal;
import com.concirrus.submissionAdapter.utils.CurrencyConverter;
import com.concirrus.submissionAdapter.utils.SlackNotifier;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class RecomputationServiceTest {

    @Mock
    private AccountRepository accountRepository;
    @Mock
    private AccountDal accountDal;
    @Mock
    private CurrencyConverter currencyConverter;
    @Mock
    private LocationRepository locationRepository;
    @Mock
    private PoliticalViolenceUtils politicalViolenceUtils;
    @Mock
    private SlackNotifier slackNotifier;
    @Mock
    private BlastZoneProcessorSal blastZoneProcessorSal;

    @InjectMocks
    private RecomputationService recomputationService;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void recomputeAccounts_shouldReturnSuccess() {
        Account acc1 = new Account();
        acc1.setPolicyCurrency("USD");
        List<Account> accounts = List.of(acc1);
        Page<Account> page = new PageImpl<>(accounts);

        when(accountRepository.findAll(any(Pageable.class)))
                .thenReturn(page)
                .thenReturn(Page.empty());
        when(currencyConverter.getExchangeRates(anyList(), anyString()))
                .thenReturn(Map.of("USD", 1.0));

        RecomputationService.RecomputationResult result = recomputationService.recomputeAccounts();

        assertTrue(result.isSuccess());
        assertEquals("accounts", result.getType());
        assertEquals(1, result.getProcessedCount());
        verify(politicalViolenceUtils).updateAccountMonetaryValues(eq(accounts), anyMap());
    }

    @Test
    void recomputeLocations_shouldReturnSuccess() {
        Location location = new Location();
        List<Location> locations = List.of(location);
        Page<Location> page = new PageImpl<>(locations);

        when(locationRepository.findAll(any(Pageable.class)))
                .thenReturn(page)
                .thenReturn(Page.empty());

        RecomputationService.RecomputationResult result = recomputationService.recomputeLocations();

        assertTrue(result.isSuccess());
        assertEquals("locations", result.getType());
        assertEquals(1, result.getProcessedCount());
        verify(politicalViolenceUtils).updateLocationMonetaryValues(eq(locations), any());
    }

    @Test
    void recomputeBlastZones_shouldReturnSuccess() throws Exception {
        List<String> submissionIds = List.of("sub1", "sub2");
        when(accountDal.findDistinctSubmissionIds()).thenReturn(submissionIds);

        RecomputationService.RecomputationResult result = recomputationService.recomputeBlastZones();

        assertTrue(result.isSuccess());
        assertEquals("blast zones", result.getType());
        assertEquals(2, result.getProcessedCount());
        verify(blastZoneProcessorSal, times(2)).clearSubmissionData(anyString());
        verify(blastZoneProcessorSal).recomputeBlastZoneAttributes();
    }

    @Test
    void recomputeAccounts_shouldReturnFailure_onException() {
        when(accountRepository.findAll(any(Pageable.class)))
                .thenThrow(new RuntimeException("DB error"));

        RecomputationService.RecomputationResult result = recomputationService.recomputeAccounts();

        assertFalse(result.isSuccess());
        assertEquals("accounts", result.getType());
        assertEquals(0, result.getProcessedCount());
        assertTrue(result.getErrorMessage().contains("DB error"));
    }

    @Test
    void recomputeAll_shouldLogFailuresAndSendSlackAlerts() {
        // Given
        RecomputationService spyService = Mockito.spy(recomputationService);

        doReturn(RecomputationService.RecomputationResult.success("accounts", 1)).when(spyService).recomputeAccounts();
        doReturn(RecomputationService.RecomputationResult.failure("locations", "location error")).when(spyService).recomputeLocations();
        doReturn(RecomputationService.RecomputationResult.success("blast zones", 1)).when(spyService).recomputeBlastZones();

        // When
        spyService.recomputeAll();

        // Then
        verify(slackNotifier).sendAlert(contains("Failed to recompute locations"));
    }
}