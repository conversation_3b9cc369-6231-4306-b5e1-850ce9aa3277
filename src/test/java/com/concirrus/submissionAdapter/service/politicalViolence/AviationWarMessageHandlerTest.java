package com.concirrus.submissionAdapter.service.politicalViolence;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import com.concirrus.submissionAdapter.dal.AccountRepository;
import com.concirrus.submissionAdapter.dal.AggregationJobRepository;
import com.concirrus.submissionAdapter.dal.LocationDal;
import com.concirrus.submissionAdapter.dal.LocationRepository;
import com.concirrus.submissionAdapter.dto.currencyExchange.LocationUpdateResult;
import com.concirrus.submissionAdapter.dto.enums.ProcessingStatus;
import com.concirrus.submissionAdapter.dto.enums.ProductType;
import com.concirrus.submissionAdapter.dto.enums.State;
import com.concirrus.submissionAdapter.dto.enums.UpdateType;
import com.concirrus.submissionAdapter.dto.submission.Quote;
import com.concirrus.submissionAdapter.dto.submission.Submission;
import com.concirrus.submissionAdapter.dto.workflow.SubmissionUpdateEvent;
import com.concirrus.submissionAdapter.model.Account;
import com.concirrus.submissionAdapter.model.AggregationJob;
import com.concirrus.submissionAdapter.sal.BlastZoneProcessorSal;
import com.concirrus.submissionAdapter.sal.QuoteServiceSal;
import com.concirrus.submissionAdapter.sal.SubmissionHandlerSal;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;
import java.util.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("AviationWarMessageHandler Tests")
class AviationWarMessageHandlerTest {

    @Mock
    private AccountRepository accountRepository;

    @Mock
    private LocationRepository locationRepository;

    @Mock
    private SubmissionHandlerSal submissionHandlerSal;

    @Mock
    private QuoteServiceSal quoteServiceSal;

    @Mock
    private AggregationWorkflowService aggregationWorkflowService;

    @Mock
    private LocationDal locationDal;

    @Mock
    private AggregationJobRepository aggregationJobRepository;

    @Mock
    private PoliticalViolenceUtils politicalViolenceUtils;

    @Mock
    private BlastZoneProcessorSal blastZoneProcessorSal;

    @InjectMocks
    private AviationWarMessageHandler aviationWarMessageHandler;

    private Submission testSubmission;
    private Quote testQuote;
    private Account testAccount;
    private SubmissionUpdateEvent testEvent;
    private AggregationJob testAggregationJob;

    @BeforeEach
    void setUp() {
        // Setup test submission
        testSubmission = new Submission();
        testSubmission.setSubmissionId("SUB123");
        testSubmission.setClientId("CLIENT123");
        testSubmission.setState(State.IN_REVIEW);
        testSubmission.setCoverageType("POLITICAL_VIOLENCE");
        testSubmission.setLineOfBusiness("POLITICAL_VIOLENCE");

        // Setup test quote
        testQuote = new Quote();
        testQuote.setQuoteId("QUOTE123");
        testQuote.setSubmissionID("SUB123");
        testQuote.setClientId("CLIENT123");
        testQuote.setStatus(String.valueOf(State.IN_REVIEW));

        // Setup test account
        testAccount = new Account();
        testAccount.setSubmissionId("SUB123");
        testAccount.setQuoteId("QUOTE123");
        testAccount.setState(State.IN_REVIEW);

        // Setup test event
        testEvent = new SubmissionUpdateEvent();
        testEvent.setSubmissionId("SUB123");
        testEvent.setClientId("CLIENT123");
        testEvent.setQuoteId("QUOTE123");
        testEvent.setUpdateType("SUBMISSION_REVIEWED");

        // Setup test aggregation job
        testAggregationJob = AggregationJob.builder()
                .jobId("JOB123")
                .submissionId("SUB123")
                .clientId("CLIENT123")
                .updateType(UpdateType.SUBMISSION_REVIEWED)
                .quoteId("QUOTE123")
                .createdAt(Instant.now())
                .build();
    }

    @Nested
    @DisplayName("Basic Handler Tests")
    class BasicHandlerTests {

        @Test
        @DisplayName("Should return correct product type")
        void shouldReturnCorrectProductType() {
            // When
            ProductType result = aviationWarMessageHandler.getProductType();

            // Then
            assertEquals(ProductType.AVIATION_WAR, result);
        }
    }

    @Nested
    @DisplayName("Process Submission Update Tests")
    class ProcessSubmissionUpdateTests {

        @Test
        @DisplayName("Should throw IllegalArgumentException for null update type")
        void shouldThrowIllegalArgumentExceptionForNullUpdateType() {
            // Given
            testEvent.setUpdateType(null);

            // When & Then
            IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                    aviationWarMessageHandler.processSubmissionUpdate(testEvent));

            assertEquals("Invalid update type: null", exception.getMessage());
        }

        @Test
        @DisplayName("Should throw IllegalArgumentException for empty update type")
        void shouldThrowIllegalArgumentExceptionForEmptyUpdateType() {
            // Given
            testEvent.setUpdateType("");

            // When & Then
            IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                    aviationWarMessageHandler.processSubmissionUpdate(testEvent));

            assertEquals("Invalid update type: ", exception.getMessage());
        }

        @Test
        @DisplayName("Should throw IllegalArgumentException for invalid update type")
        void shouldThrowIllegalArgumentExceptionForInvalidUpdateType() {
            // Given
            testEvent.setUpdateType("INVALID_TYPE");

            // When & Then
            IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                    aviationWarMessageHandler.processSubmissionUpdate(testEvent));

            assertEquals("Invalid update type: INVALID_TYPE", exception.getMessage());
        }

        @Test
        @DisplayName("Should throw RuntimeException when submission not found")
        void shouldThrowRuntimeExceptionWhenSubmissionNotFound() {
            // Given
            when(submissionHandlerSal.getSubmissionById("SUB123", "CLIENT123"))
                    .thenReturn(null);

            // When & Then
            RuntimeException exception = assertThrows(RuntimeException.class, () ->
                    aviationWarMessageHandler.processSubmissionUpdate(testEvent));

            assertEquals("Failed to process submission update for: SUB123", exception.getMessage());
            verify(submissionHandlerSal).getSubmissionById("SUB123", "CLIENT123");
        }

        @Test
        @DisplayName("Should skip processing when submission is in INBOX state")
        void shouldSkipProcessingWhenSubmissionIsInInboxState() {
            // Given
            testSubmission.setState(State.INBOX);
            when(submissionHandlerSal.getSubmissionById("SUB123", "CLIENT123"))
                    .thenReturn(testSubmission);

            // When
            aviationWarMessageHandler.processSubmissionUpdate(testEvent);

            // Then
            verify(submissionHandlerSal).getSubmissionById("SUB123", "CLIENT123");
            verifyNoInteractions(aggregationJobRepository, aggregationWorkflowService);
        }

        @Test
        @DisplayName("Should skip processing when coverage type is not POLITICAL_VIOLENCE")
        void shouldSkipProcessingWhenCoverageTypeIsNotPoliticalViolence() {
            // Given
            testSubmission.setLineOfBusiness("OTHER_COVERAGE");
            when(submissionHandlerSal.getSubmissionById("SUB123", "CLIENT123"))
                    .thenReturn(testSubmission);

            // When
            aviationWarMessageHandler.processSubmissionUpdate(testEvent);

            // Then
            verify(submissionHandlerSal).getSubmissionById("SUB123", "CLIENT123");
            verifyNoInteractions(aggregationJobRepository, aggregationWorkflowService);
        }

        @Test
        @DisplayName("Should process valid submission update successfully")
        void shouldProcessValidSubmissionUpdateSuccessfully() {
            // Given
            when(submissionHandlerSal.getSubmissionById("SUB123", "CLIENT123"))
                    .thenReturn(testSubmission);
            when(accountRepository.existsBySubmissionId("SUB123")).thenReturn(false);
            when(locationRepository.existsBySubmissionId("SUB123")).thenReturn(false);
            when(quoteServiceSal.getAllQuotesBySubmissionId("SUB123", "CLIENT123"))
                    .thenReturn(Collections.singletonList(testQuote));
            when(politicalViolenceUtils.ingestLocations(eq("SUB123"), eq("CLIENT123"), eq(State.IN_REVIEW), eq(UpdateType.SUBMISSION_REVIEWED)))
                    .thenReturn(5);

            // When
            aviationWarMessageHandler.processSubmissionUpdate(testEvent);

            // Then
            verify(submissionHandlerSal).getSubmissionById("SUB123", "CLIENT123");
            verify(aggregationJobRepository, times(1)).save(any(AggregationJob.class));
            verify(aggregationWorkflowService).createTasksAndSendEvents(any(AggregationJob.class), eq(UpdateType.SUBMISSION_REVIEWED), isNull());
        }

        @Test
        @DisplayName("Should wrap and rethrow unexpected exceptions")
        void shouldWrapAndRethrowUnexpectedExceptions() {
            // Given
            when(submissionHandlerSal.getSubmissionById("SUB123", "CLIENT123"))
                    .thenThrow(new RuntimeException("Database connection failed"));

            // When & Then
            RuntimeException exception = assertThrows(RuntimeException.class, () ->
                    aviationWarMessageHandler.processSubmissionUpdate(testEvent));

            assertEquals("Failed to process submission update for: SUB123", exception.getMessage());
            assertEquals("Database connection failed", exception.getCause().getMessage());
        }
    }

    @Nested
    @DisplayName("Submission Level Events Tests")
    class SubmissionLevelEventsTests {

        @Test
        @DisplayName("Should process SUBMISSION_REVIEWED with existing account and locations - successful clear")
        void shouldProcessSubmissionReviewedWithExistingAccountAndLocationsSuccessfulClear() {
            // Given
            testEvent.setUpdateType("SUBMISSION_REVIEWED");
            when(submissionHandlerSal.getSubmissionById("SUB123", "CLIENT123"))
                    .thenReturn(testSubmission);
            when(accountRepository.existsBySubmissionId("SUB123")).thenReturn(true);
            when(locationRepository.existsBySubmissionId("SUB123")).thenReturn(true);
            when(blastZoneProcessorSal.clearSubmissionData("SUB123")).thenReturn(true); // Successful clear
            when(quoteServiceSal.getAllQuotesBySubmissionId("SUB123", "CLIENT123"))
                    .thenReturn(Collections.singletonList(testQuote));
            when(politicalViolenceUtils.ingestLocations(eq("SUB123"), eq("CLIENT123"), eq(State.IN_REVIEW), eq(UpdateType.SUBMISSION_REVIEWED)))
                    .thenReturn(3);

            // When
            aviationWarMessageHandler.processSubmissionUpdate(testEvent);

            // Then
            verify(accountRepository).deleteBySubmissionId("SUB123");
            verify(locationRepository).deleteBySubmissionId("SUB123");
            verify(blastZoneProcessorSal).clearSubmissionData("SUB123");
            // Should NOT call createTasksAndSendEvents with LOCATION_DELETED since clear was successful
            verify(aggregationWorkflowService, never()).createTasksAndSendEvents(any(AggregationJob.class), eq(UpdateType.LOCATION_DELETED), isNull());
            verify(politicalViolenceUtils).ingestQuote(testSubmission, testQuote);
            verify(aggregationWorkflowService).createMiAnalysisJob(any(AggregationJob.class));
            verify(aggregationWorkflowService).createTasksAndSendEvents(any(AggregationJob.class), eq(UpdateType.SUBMISSION_REVIEWED), isNull());
        }

        @Test
        @DisplayName("Should process SUBMISSION_REVIEWED with existing account and locations - failed clear")
        void shouldProcessSubmissionReviewedWithExistingAccountAndLocationsFailedClear() {
            // Given
            testEvent.setUpdateType("SUBMISSION_REVIEWED");
            when(submissionHandlerSal.getSubmissionById("SUB123", "CLIENT123"))
                    .thenReturn(testSubmission);
            when(accountRepository.existsBySubmissionId("SUB123")).thenReturn(true);
            when(locationRepository.existsBySubmissionId("SUB123")).thenReturn(true);
            when(blastZoneProcessorSal.clearSubmissionData("SUB123")).thenReturn(false); // Failed clear
            when(quoteServiceSal.getAllQuotesBySubmissionId("SUB123", "CLIENT123"))
                    .thenReturn(Collections.singletonList(testQuote));
            when(politicalViolenceUtils.ingestLocations(eq("SUB123"), eq("CLIENT123"), eq(State.IN_REVIEW), eq(UpdateType.SUBMISSION_REVIEWED)))
                    .thenReturn(3);

            // When
            aviationWarMessageHandler.processSubmissionUpdate(testEvent);

            // Then
            verify(accountRepository).deleteBySubmissionId("SUB123");
            verify(locationRepository).deleteBySubmissionId("SUB123");
            verify(blastZoneProcessorSal).clearSubmissionData("SUB123");
            // Should call createTasksAndSendEvents with LOCATION_DELETED since clear failed
            verify(aggregationWorkflowService).createTasksAndSendEvents(any(AggregationJob.class), eq(UpdateType.LOCATION_DELETED), isNull());
            verify(politicalViolenceUtils).ingestQuote(testSubmission, testQuote);
            verify(aggregationWorkflowService).createMiAnalysisJob(any(AggregationJob.class));
            verify(aggregationWorkflowService).createTasksAndSendEvents(any(AggregationJob.class), eq(UpdateType.SUBMISSION_REVIEWED), isNull());
        }

        @Test
        @DisplayName("Should process SUBMISSION_REVIEWED with no locations found")
        void shouldProcessSubmissionReviewedWithNoLocationsFound() {
            // Given
            testEvent.setUpdateType("SUBMISSION_REVIEWED");
            when(submissionHandlerSal.getSubmissionById("SUB123", "CLIENT123"))
                    .thenReturn(testSubmission);
            when(accountRepository.existsBySubmissionId("SUB123")).thenReturn(false);
            when(locationRepository.existsBySubmissionId("SUB123")).thenReturn(false);
            when(quoteServiceSal.getAllQuotesBySubmissionId("SUB123", "CLIENT123"))
                    .thenReturn(Collections.singletonList(testQuote));
            when(politicalViolenceUtils.ingestLocations(eq("SUB123"), eq("CLIENT123"), eq(State.IN_REVIEW), eq(UpdateType.SUBMISSION_REVIEWED)))
                    .thenReturn(0);

            // When
            aviationWarMessageHandler.processSubmissionUpdate(testEvent);

            // Then
            ArgumentCaptor<AggregationJob> jobCaptor = ArgumentCaptor.forClass(AggregationJob.class);
            verify(aggregationJobRepository, times(2)).save(jobCaptor.capture());
            
            AggregationJob savedJob = jobCaptor.getAllValues().get(1); // Get the second save call
            assertEquals(0, savedJob.getCountOfLocations());
            assertEquals(ProcessingStatus.COMPLETED, savedJob.getStatus());
            assertEquals("No locations found for submission SUB123", savedJob.getComment());
            
            verifyNoInteractions(aggregationWorkflowService);
        }
        @Test
        @DisplayName("Should process SUBMISSION_DELETED successfully")
        void shouldProcessSubmissionDeletedSuccessfully() {
            // Given
            testEvent.setUpdateType("SUBMISSION_DELETED");
            when(accountRepository.findBySubmissionId("SUB123"))
                    .thenReturn(Collections.singletonList(testAccount));

            // When
            aviationWarMessageHandler.processSubmissionUpdate(testEvent);

            // Then
            verify(accountRepository).deleteAll(Collections.singletonList(testAccount));
            verify(locationRepository).deleteBySubmissionId("SUB123");
            verify(aggregationWorkflowService).createTasksAndSendEvents(any(AggregationJob.class), eq(UpdateType.SUBMISSION_DELETED), isNull());
        }

        @Test
        @DisplayName("Should process SUBMISSION_BOUND successfully")
        void shouldProcessSubmissionBoundSuccessfully() {
            // Given
            testEvent.setUpdateType("SUBMISSION_BOUND");
            when(submissionHandlerSal.getSubmissionById("SUB123", "CLIENT123"))
                    .thenReturn(testSubmission);
            when(quoteServiceSal.getAllQuotesBySubmissionId("SUB123", "CLIENT123"))
                    .thenReturn(Collections.singletonList(testQuote));
            when(accountRepository.findBySubmissionIdAndQuoteId("SUB123", "QUOTE123"))
                    .thenReturn(Optional.of(testAccount));

            // When
            aviationWarMessageHandler.processSubmissionUpdate(testEvent);

            // Then
            verify(accountRepository).save(testAccount);
            verify(locationDal).updateLocationState("SUB123", State.BOUND);
            verify(aggregationWorkflowService).createTasksAndSendEvents(any(AggregationJob.class), eq(UpdateType.SUBMISSION_BOUND), isNull());
        }

        @Test
        @DisplayName("Should process SUBMISSION_BOUND with new account creation")
        void shouldProcessSubmissionBoundWithNewAccountCreation() {
            // Given
            testEvent.setUpdateType("SUBMISSION_BOUND");
            when(submissionHandlerSal.getSubmissionById("SUB123", "CLIENT123"))
                    .thenReturn(testSubmission);
            when(quoteServiceSal.getAllQuotesBySubmissionId("SUB123", "CLIENT123"))
                    .thenReturn(Collections.singletonList(testQuote));
            when(accountRepository.findBySubmissionIdAndQuoteId("SUB123", "QUOTE123"))
                    .thenReturn(Optional.empty());
            when(politicalViolenceUtils.ingestQuote(testSubmission, testQuote))
                    .thenReturn(testAccount);

            // When
            aviationWarMessageHandler.processSubmissionUpdate(testEvent);

            // Then
            verify(politicalViolenceUtils).ingestQuote(testSubmission, testQuote);
            verify(accountRepository).save(any(Account.class));
            verify(locationDal).updateLocationState("SUB123", State.BOUND);
        }
    }

    @Nested
    @DisplayName("Quote Level Events Tests")
    class QuoteLevelEventsTests {

        @Test
        @DisplayName("Should process QUOTE_CREATED successfully")
        void shouldProcessQuoteCreatedSuccessfully() {
            // Given
            testEvent.setUpdateType("QUOTE_CREATED");
            when(submissionHandlerSal.getSubmissionById("SUB123", "CLIENT123"))
                    .thenReturn(testSubmission);
            when(quoteServiceSal.getQuoteById("QUOTE123", "CLIENT123"))
                    .thenReturn(testQuote);
            when(politicalViolenceUtils.ingestQuote(testSubmission, testQuote))
                    .thenReturn(testAccount);

            // When
            aviationWarMessageHandler.processSubmissionUpdate(testEvent);

            // Then
            verify(quoteServiceSal).getQuoteById("QUOTE123", "CLIENT123");
            verify(politicalViolenceUtils).ingestQuote(testSubmission, testQuote);
            verify(aggregationWorkflowService).createTasksAndSendEvents(any(AggregationJob.class), eq(UpdateType.QUOTE_CREATED), isNull());
        }

        @Test
        @DisplayName("Should throw RuntimeException when quote not found for QUOTE_CREATED")
        void shouldHandleExceptionWhenQuoteNotFoundForQuoteCreated() {
            // Given
            testEvent.setUpdateType("QUOTE_CREATED");
            when(submissionHandlerSal.getSubmissionById("SUB123", "CLIENT123"))
                    .thenReturn(testSubmission);
            when(quoteServiceSal.getQuoteById("QUOTE123", "CLIENT123"))
                    .thenReturn(null);

            // When & Then
            RuntimeException exception = assertThrows(RuntimeException.class, () ->
                    aviationWarMessageHandler.processSubmissionUpdate(testEvent));

            assertEquals("Failed to process submission update for: SUB123", exception.getMessage());
            assertTrue(exception.getCause().getMessage().contains("Failed to process update type: QUOTE_CREATED"));

            ArgumentCaptor<AggregationJob> jobCaptor = ArgumentCaptor.forClass(AggregationJob.class);
            verify(aggregationJobRepository, times(1)).save(jobCaptor.capture());

            AggregationJob failedJob = jobCaptor.getAllValues().getFirst();
            assertEquals(ProcessingStatus.FAILED, failedJob.getStatus());
            assertTrue(failedJob.getComment().contains("Quote not found: QUOTE123"));
        }

        @Test
        @DisplayName("Should process QUOTE_DELETED successfully")
        void shouldProcessQuoteDeletedSuccessfully() {
            // Given
            testEvent.setUpdateType("QUOTE_DELETED");
            when(submissionHandlerSal.getSubmissionById("SUB123", "CLIENT123"))
                    .thenReturn(testSubmission);
            when(accountRepository.findBySubmissionIdAndQuoteId("SUB123", "QUOTE123"))
                    .thenReturn(Optional.of(testAccount));

            // When
            aviationWarMessageHandler.processSubmissionUpdate(testEvent);

            // Then
            verify(accountRepository).delete(testAccount);
            verify(aggregationWorkflowService).createTasksAndSendEvents(any(AggregationJob.class), eq(UpdateType.QUOTE_DELETED), isNull());
        }

        @Test
        @DisplayName("Should skip QUOTE_DELETED when no account found")
        void shouldSkipQuoteDeletedWhenNoAccountFound() {
            // Given
            testEvent.setUpdateType("QUOTE_DELETED");
            when(submissionHandlerSal.getSubmissionById("SUB123", "CLIENT123"))
                    .thenReturn(testSubmission);
            when(accountRepository.findBySubmissionIdAndQuoteId("SUB123", "QUOTE123"))
                    .thenReturn(Optional.empty());

            // When
            aviationWarMessageHandler.processSubmissionUpdate(testEvent);

            // Then
            ArgumentCaptor<AggregationJob> jobCaptor = ArgumentCaptor.forClass(AggregationJob.class);
            verify(aggregationJobRepository, times(2)).save(jobCaptor.capture());

            AggregationJob savedJob = jobCaptor.getAllValues().get(1);
            assertEquals(ProcessingStatus.COMPLETED, savedJob.getStatus());
            assertEquals("No account found for quote QUOTE123", savedJob.getComment());
            assertEquals(0, savedJob.getCountOfLocations());

            verify(accountRepository, never()).delete(any());
            verifyNoInteractions(aggregationWorkflowService);
        }

        @Test
        @DisplayName("Should process QUOTE_UPDATED with existing account and relevant changes")
        void shouldProcessQuoteUpdatedWithExistingAccountAndRelevantChanges() {
            // Given
            testEvent.setUpdateType("QUOTE_UPDATED");
            when(submissionHandlerSal.getSubmissionById("SUB123", "CLIENT123"))
                    .thenReturn(testSubmission);
            when(accountRepository.findBySubmissionIdAndQuoteId("SUB123", "QUOTE123"))
                    .thenReturn(Optional.of(testAccount));
            when(quoteServiceSal.getQuoteById("QUOTE123", "CLIENT123"))
                    .thenReturn(testQuote);
            when(politicalViolenceUtils.haveRelevantFieldsChangedForQuote(testAccount, testQuote, testSubmission))
                    .thenReturn(true);

            // When
            aviationWarMessageHandler.processSubmissionUpdate(testEvent);

            // Then
            verify(politicalViolenceUtils).haveRelevantFieldsChangedForQuote(testAccount, testQuote, testSubmission);
            verify(aggregationWorkflowService).createTasksAndSendEvents(any(AggregationJob.class), eq(UpdateType.QUOTE_UPDATED), isNull());
        }

        @Test
        @DisplayName("Should process QUOTE_UPDATED with new account creation")
        void shouldProcessQuoteUpdatedWithNewAccountCreation() {
            // Given
            testEvent.setUpdateType("QUOTE_UPDATED");
            when(submissionHandlerSal.getSubmissionById("SUB123", "CLIENT123"))
                    .thenReturn(testSubmission);
            when(accountRepository.findBySubmissionIdAndQuoteId("SUB123", "QUOTE123"))
                    .thenReturn(Optional.empty());
            when(quoteServiceSal.getQuoteById("QUOTE123", "CLIENT123"))
                    .thenReturn(testQuote);
            when(politicalViolenceUtils.ingestQuote(testSubmission, testQuote))
                    .thenReturn(testAccount);

            // When
            aviationWarMessageHandler.processSubmissionUpdate(testEvent);

            // Then
            verify(politicalViolenceUtils).ingestQuote(testSubmission, testQuote);
            verify(aggregationWorkflowService).createTasksAndSendEvents(any(AggregationJob.class), eq(UpdateType.QUOTE_UPDATED), isNull());
        }

        @Test
        @DisplayName("Should skip QUOTE_UPDATED when no relevant field changes")
        void shouldSkipQuoteUpdatedWhenNoRelevantFieldChanges() {
            // Given
            testEvent.setUpdateType("QUOTE_UPDATED");
            when(submissionHandlerSal.getSubmissionById("SUB123", "CLIENT123"))
                    .thenReturn(testSubmission);
            when(accountRepository.findBySubmissionIdAndQuoteId("SUB123", "QUOTE123"))
                    .thenReturn(Optional.of(testAccount));
            when(quoteServiceSal.getQuoteById("QUOTE123", "CLIENT123"))
                    .thenReturn(testQuote);
            when(politicalViolenceUtils.haveRelevantFieldsChangedForQuote(testAccount, testQuote, testSubmission))
                    .thenReturn(false);

            // When
            aviationWarMessageHandler.processSubmissionUpdate(testEvent);

            // Then
            ArgumentCaptor<AggregationJob> jobCaptor = ArgumentCaptor.forClass(AggregationJob.class);
            verify(aggregationJobRepository, times(2)).save(jobCaptor.capture());

            AggregationJob savedJob = jobCaptor.getAllValues().get(1);
            assertEquals(ProcessingStatus.COMPLETED, savedJob.getStatus());
            assertEquals("No relevant field changes detected for quote QUOTE123", savedJob.getComment());
            assertEquals(0, savedJob.getCountOfLocations());

            verifyNoInteractions(aggregationWorkflowService);
        }

        @Test
        @DisplayName("Should throw RuntimeException when updated quote not found")
        void shouldThrowRuntimeExceptionWhenUpdatedQuoteNotFound() {
            // Given
            testEvent.setUpdateType("QUOTE_UPDATED");
            when(submissionHandlerSal.getSubmissionById("SUB123", "CLIENT123"))
                    .thenReturn(testSubmission);
            when(accountRepository.findBySubmissionIdAndQuoteId("SUB123", "QUOTE123"))
                    .thenReturn(Optional.of(testAccount));
            when(quoteServiceSal.getQuoteById("QUOTE123", "CLIENT123"))
                    .thenReturn(null);

            // When & Then
            RuntimeException exception = assertThrows(RuntimeException.class, () ->
                    aviationWarMessageHandler.processSubmissionUpdate(testEvent));

            assertEquals("Failed to process submission update for: SUB123", exception.getMessage());
            assertTrue(exception.getCause().getMessage().contains("Failed to process update type: QUOTE_UPDATED"));

            ArgumentCaptor<AggregationJob> jobCaptor = ArgumentCaptor.forClass(AggregationJob.class);
            verify(aggregationJobRepository, times(1)).save(jobCaptor.capture());

            AggregationJob failedJob = jobCaptor.getAllValues().getFirst();
            assertEquals(ProcessingStatus.FAILED, failedJob.getStatus());
            assertTrue(failedJob.getComment().contains("Updated quote not found: QUOTE123"));
        }
    }

    @Nested
    @DisplayName("Location Level Events Tests")
    class LocationLevelEventsTests {

        @Test
        @DisplayName("Should process LOCATION_UPLOADED successfully")
        void shouldProcessLocationUploadedSuccessfully() {
            // Given
            testEvent.setUpdateType("LOCATION_UPLOADED");
            when(submissionHandlerSal.getSubmissionById("SUB123", "CLIENT123"))
                    .thenReturn(testSubmission);
            when(accountRepository.findOneBySubmissionId("SUB123"))
                    .thenReturn(Optional.of(testAccount));
            when(politicalViolenceUtils.ingestLocations(eq("SUB123"), eq("CLIENT123"), eq(State.IN_REVIEW), eq(UpdateType.LOCATION_CREATED)))
                    .thenReturn(5);

            // When
            aviationWarMessageHandler.processSubmissionUpdate(testEvent);

            // Then
            verify(locationRepository).deleteBySubmissionId("SUB123");
            verify(aggregationWorkflowService).createTasksAndSendEvents(any(AggregationJob.class), eq(UpdateType.LOCATION_DELETED), isNull());
            verify(politicalViolenceUtils).ingestLocations("SUB123", "CLIENT123", State.IN_REVIEW, UpdateType.LOCATION_CREATED);
            verify(aggregationWorkflowService).createTasksAndSendEvents(any(AggregationJob.class), eq(UpdateType.LOCATION_CREATED), isNull());

            ArgumentCaptor<AggregationJob> jobCaptor = ArgumentCaptor.forClass(AggregationJob.class);
            verify(aggregationJobRepository, times(1)).save(jobCaptor.capture());
            assertEquals(5, jobCaptor.getAllValues().getFirst().getCountOfLocations());
        }

        @Test
        @DisplayName("Should skip LOCATION_UPLOADED when no account found")
        void shouldSkipLocationUploadedWhenNoAccountFound() {
            // Given
            testEvent.setUpdateType("LOCATION_UPLOADED");
            when(submissionHandlerSal.getSubmissionById("SUB123", "CLIENT123"))
                    .thenReturn(testSubmission);
            when(accountRepository.findOneBySubmissionId("SUB123"))
                    .thenReturn(Optional.empty());

            // When
            aviationWarMessageHandler.processSubmissionUpdate(testEvent);

            // Then
            verify(accountRepository).findOneBySubmissionId("SUB123");
            verifyNoInteractions(locationRepository, politicalViolenceUtils, aggregationWorkflowService);
        }

        @Test
        @DisplayName("Should process LOCATION_UPLOADED with no locations found")
        void shouldProcessLocationUploadedWithNoLocationsFound() {
            // Given
            testEvent.setUpdateType("LOCATION_UPLOADED");
            when(submissionHandlerSal.getSubmissionById("SUB123", "CLIENT123"))
                    .thenReturn(testSubmission);
            when(accountRepository.findOneBySubmissionId("SUB123"))
                    .thenReturn(Optional.of(testAccount));
            when(politicalViolenceUtils.ingestLocations(eq("SUB123"), eq("CLIENT123"), eq(State.IN_REVIEW), eq(UpdateType.LOCATION_CREATED)))
                    .thenReturn(0);

            // When
            aviationWarMessageHandler.processSubmissionUpdate(testEvent);

            // Then
            verify(locationRepository).deleteBySubmissionId("SUB123");
            verify(aggregationWorkflowService).createTasksAndSendEvents(any(AggregationJob.class), eq(UpdateType.LOCATION_DELETED), isNull());

            ArgumentCaptor<AggregationJob> jobCaptor = ArgumentCaptor.forClass(AggregationJob.class);
            verify(aggregationJobRepository, times(2)).save(jobCaptor.capture());

            AggregationJob savedJob = jobCaptor.getAllValues().get(1);
            assertEquals(0, savedJob.getCountOfLocations());
            assertEquals(ProcessingStatus.COMPLETED, savedJob.getStatus());
            assertEquals("No locations found for submission SUB123", savedJob.getComment());

            verify(aggregationWorkflowService, times(1)).createTasksAndSendEvents(any(), eq(UpdateType.LOCATION_DELETED), isNull());
        }
        @Test
        @DisplayName("Should process LOCATION_UPDATED with coordinate and data updates")
        void shouldProcessLocationUpdatedWithCoordinateAndDataUpdates() {
            // Given
            testEvent.setUpdateType("LOCATION_UPDATED");
            LocationUpdateResult updateResult = new LocationUpdateResult();
            updateResult.addCoordinateUpdate("LOC1");
            updateResult.addCoordinateUpdate("LOC2");
            updateResult.addDataUpdate("LOC3");

            when(submissionHandlerSal.getSubmissionById("SUB123", "CLIENT123"))
                    .thenReturn(testSubmission);
            when(politicalViolenceUtils.processAllLocationPages("SUB123", "CLIENT123"))
                    .thenReturn(updateResult);

            // When
            aviationWarMessageHandler.processSubmissionUpdate(testEvent);

            // Then
            verify(politicalViolenceUtils).processAllLocationPages("SUB123", "CLIENT123");
            verify(aggregationWorkflowService).createTasksAndSendEvents(
                    any(AggregationJob.class),
                    eq(UpdateType.LOCATION_COORDINATES_UPDATED),
                    eq(updateResult.getCoordinateUpdatedLocations())
            );
            verify(aggregationWorkflowService).createTasksAndSendEvents(
                    any(AggregationJob.class),
                    eq(UpdateType.LOCATION_DATA_UPDATED),
                    eq(updateResult.getDataUpdatedLocations())
            );

            ArgumentCaptor<AggregationJob> jobCaptor = ArgumentCaptor.forClass(AggregationJob.class);
            verify(aggregationJobRepository, times(1)).save(jobCaptor.capture());
            assertEquals(3, jobCaptor.getAllValues().getFirst().getCountOfLocations());
        }

        @Test
        @DisplayName("Should process LOCATION_UPDATED with only coordinate updates")
        void shouldProcessLocationUpdatedWithOnlyCoordinateUpdates() {
            // Given
            testEvent.setUpdateType("LOCATION_UPDATED");
            LocationUpdateResult updateResult = new LocationUpdateResult();
            updateResult.addCoordinateUpdate("LOC1");

            when(submissionHandlerSal.getSubmissionById("SUB123", "CLIENT123"))
                    .thenReturn(testSubmission);
            when(politicalViolenceUtils.processAllLocationPages("SUB123", "CLIENT123"))
                    .thenReturn(updateResult);

            // When
            aviationWarMessageHandler.processSubmissionUpdate(testEvent);

            // Then
            verify(aggregationWorkflowService).createTasksAndSendEvents(
                    any(AggregationJob.class),
                    eq(UpdateType.LOCATION_COORDINATES_UPDATED),
                    eq(updateResult.getCoordinateUpdatedLocations())
            );
            verify(aggregationWorkflowService, never()).createTasksAndSendEvents(
                    any(AggregationJob.class),
                    eq(UpdateType.LOCATION_DATA_UPDATED),
                    any()
            );
        }

        @Test
        @DisplayName("Should process LOCATION_UPDATED with no updates")
        void shouldProcessLocationUpdatedWithNoUpdates() {
            // Given
            testEvent.setUpdateType("LOCATION_UPDATED");
            LocationUpdateResult updateResult = new LocationUpdateResult(); // Empty result

            when(submissionHandlerSal.getSubmissionById("SUB123", "CLIENT123"))
                    .thenReturn(testSubmission);
            when(politicalViolenceUtils.processAllLocationPages("SUB123", "CLIENT123"))
                    .thenReturn(updateResult);

            // When
            aviationWarMessageHandler.processSubmissionUpdate(testEvent);

            // Then
            verify(politicalViolenceUtils).processAllLocationPages("SUB123", "CLIENT123");
            verifyNoInteractions(aggregationWorkflowService);

            ArgumentCaptor<AggregationJob> jobCaptor = ArgumentCaptor.forClass(AggregationJob.class);
            verify(aggregationJobRepository, times(1)).save(jobCaptor.capture());
            assertEquals(0, jobCaptor.getAllValues().getFirst().getCountOfLocations());
        }
    }

    @Nested
    @DisplayName("Error Handling and Edge Cases Tests")
    class ErrorHandlingAndEdgeCasesTests {

        @Test
        @DisplayName("Should handle unhandled update type gracefully")
        void shouldHandleUnhandledUpdateTypeGracefully() {
            // Given
            testEvent.setUpdateType("SOME_FUTURE_UPDATE_TYPE");


            // When
            IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                    aviationWarMessageHandler.processSubmissionUpdate(testEvent));
            assertEquals("Invalid update type: SOME_FUTURE_UPDATE_TYPE", exception.getMessage());

            // Then
            verifyNoInteractions(aggregationJobRepository);
            verifyNoInteractions(aggregationWorkflowService);
        }

        @Test
        @DisplayName("Should handle exception during update processing and set job status to FAILED")
        void shouldHandleExceptionDuringUpdateProcessingAndSetJobStatusToFailed() {
            // Given
            testEvent.setUpdateType("SUBMISSION_REVIEWED");
            when(submissionHandlerSal.getSubmissionById("SUB123", "CLIENT123"))
                    .thenReturn(testSubmission);
            when(accountRepository.existsBySubmissionId("SUB123"))
                    .thenThrow(new RuntimeException("Database connection failed"));

            // When & Then
            RuntimeException exception = assertThrows(RuntimeException.class, () ->
                    aviationWarMessageHandler.processSubmissionUpdate(testEvent));

            assertEquals("Failed to process submission update for: SUB123", exception.getMessage());

            // Verify that the aggregation job was saved with FAILED status
            ArgumentCaptor<AggregationJob> jobCaptor = ArgumentCaptor.forClass(AggregationJob.class);
            verify(aggregationJobRepository, times(1)).save(jobCaptor.capture());

            AggregationJob failedJob = jobCaptor.getAllValues().getFirst();
            assertEquals(ProcessingStatus.FAILED, failedJob.getStatus());
            assertTrue(failedJob.getComment().contains("Failed to process update type: SUBMISSION_REVIEWED"));
            assertTrue(failedJob.getComment().contains("Database connection failed"));
        }

        @Test
        @DisplayName("Should handle exception in quote processing during SUBMISSION_REVIEWED")
        void shouldHandleExceptionInQuoteProcessingDuringSubmissionReviewed() {
            // Given
            testEvent.setUpdateType("SUBMISSION_REVIEWED");
            when(submissionHandlerSal.getSubmissionById("SUB123", "CLIENT123"))
                    .thenReturn(testSubmission);
            when(accountRepository.existsBySubmissionId("SUB123")).thenReturn(false);
            when(locationRepository.existsBySubmissionId("SUB123")).thenReturn(false);
            when(quoteServiceSal.getAllQuotesBySubmissionId("SUB123", "CLIENT123"))
                    .thenReturn(Collections.singletonList(testQuote));
            when(politicalViolenceUtils.ingestQuote(testSubmission, testQuote))
                    .thenThrow(new RuntimeException("Quote processing failed"));
            when(politicalViolenceUtils.ingestLocations(eq("SUB123"), eq("CLIENT123"), eq(State.IN_REVIEW), eq(UpdateType.SUBMISSION_REVIEWED)))
                    .thenReturn(3);

            // When
            aviationWarMessageHandler.processSubmissionUpdate(testEvent);

            // Then
            verify(politicalViolenceUtils).ingestQuote(testSubmission, testQuote);
            verify(politicalViolenceUtils).ingestLocations("SUB123", "CLIENT123", State.IN_REVIEW, UpdateType.SUBMISSION_REVIEWED);
            verify(aggregationWorkflowService).createTasksAndSendEvents(any(AggregationJob.class), eq(UpdateType.SUBMISSION_REVIEWED), isNull());
        }

        @Test
        @DisplayName("Should handle empty quotes list during SUBMISSION_REVIEWED")
        void shouldHandleEmptyQuotesListDuringSubmissionReviewed() {
            // Given
            testEvent.setUpdateType("SUBMISSION_REVIEWED");
            when(submissionHandlerSal.getSubmissionById("SUB123", "CLIENT123"))
                    .thenReturn(testSubmission);
            when(accountRepository.existsBySubmissionId("SUB123")).thenReturn(false);
            when(locationRepository.existsBySubmissionId("SUB123")).thenReturn(false);
            when(quoteServiceSal.getAllQuotesBySubmissionId("SUB123", "CLIENT123"))
                    .thenReturn(Collections.emptyList());
            when(politicalViolenceUtils.ingestLocations(eq("SUB123"), eq("CLIENT123"), eq(State.IN_REVIEW), eq(UpdateType.SUBMISSION_REVIEWED)))
                    .thenReturn(2);

            // When
            aviationWarMessageHandler.processSubmissionUpdate(testEvent);

            // Then
            verify(quoteServiceSal).getAllQuotesBySubmissionId("SUB123", "CLIENT123");
            verify(politicalViolenceUtils, never()).ingestQuote(any(), any());
            verify(politicalViolenceUtils).ingestLocations("SUB123", "CLIENT123", State.IN_REVIEW, UpdateType.SUBMISSION_REVIEWED);
            verify(aggregationWorkflowService).createTasksAndSendEvents(any(AggregationJob.class), eq(UpdateType.SUBMISSION_REVIEWED), isNull());
        }

        @Test
        @DisplayName("Should handle null quotes list during SUBMISSION_REVIEWED")
        void shouldHandleNullQuotesListDuringSubmissionReviewed() {
            // Given
            testEvent.setUpdateType("SUBMISSION_REVIEWED");
            when(submissionHandlerSal.getSubmissionById("SUB123", "CLIENT123"))
                    .thenReturn(testSubmission);
            when(accountRepository.existsBySubmissionId("SUB123")).thenReturn(false);
            when(locationRepository.existsBySubmissionId("SUB123")).thenReturn(false);
            when(quoteServiceSal.getAllQuotesBySubmissionId("SUB123", "CLIENT123"))
                    .thenReturn(null);
            when(politicalViolenceUtils.ingestLocations(eq("SUB123"), eq("CLIENT123"), eq(State.IN_REVIEW), eq(UpdateType.SUBMISSION_REVIEWED)))
                    .thenReturn(1);

            // When
            aviationWarMessageHandler.processSubmissionUpdate(testEvent);

            // Then
            verify(quoteServiceSal).getAllQuotesBySubmissionId("SUB123", "CLIENT123");
            verify(politicalViolenceUtils, never()).ingestQuote(any(), any());
            verify(politicalViolenceUtils).ingestLocations("SUB123", "CLIENT123", State.IN_REVIEW, UpdateType.SUBMISSION_REVIEWED);
            verify(aggregationWorkflowService).createTasksAndSendEvents(any(AggregationJob.class), eq(UpdateType.SUBMISSION_REVIEWED), isNull());
        }

        @Test
        @DisplayName("Should handle empty accounts list during SUBMISSION_DELETED")
        void shouldHandleEmptyAccountsListDuringSubmissionDeleted() {
            // Given
            testEvent.setUpdateType("SUBMISSION_DELETED");
            when(accountRepository.findBySubmissionId("SUB123"))
                    .thenReturn(Collections.emptyList());

            // When
            aviationWarMessageHandler.processSubmissionUpdate(testEvent);

            // Then
            verify(accountRepository, never()).deleteAll(any());
            verify(locationRepository).deleteBySubmissionId("SUB123");
            verify(aggregationWorkflowService).createTasksAndSendEvents(any(AggregationJob.class), eq(UpdateType.SUBMISSION_DELETED), isNull());
        }
    }
}
