package com.concirrus.submissionAdapter.service.politicalViolence;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import com.concirrus.submissionAdapter.dal.AccountRepository;
import com.concirrus.submissionAdapter.dal.AggregationJobRepository;
import com.concirrus.submissionAdapter.dal.LocationRepository;
import com.concirrus.submissionAdapter.dal.LocationTaskRepository;
import com.concirrus.submissionAdapter.dto.enums.ProcessingStatus;
import com.concirrus.submissionAdapter.dto.enums.State;
import com.concirrus.submissionAdapter.dto.enums.UpdateType;
import com.concirrus.submissionAdapter.dto.miAnalysis.MiAnalysisJobRequest;
import com.concirrus.submissionAdapter.dto.workflow.LocationEventDTO;
import com.concirrus.submissionAdapter.dto.workflow.TaskCallBack;
import com.concirrus.submissionAdapter.eventing.producer.EventSender;
import com.concirrus.submissionAdapter.model.Account;
import com.concirrus.submissionAdapter.model.AggregationJob;
import com.concirrus.submissionAdapter.model.Location;
import com.concirrus.submissionAdapter.model.LocationTask;
import com.concirrus.submissionAdapter.sal.BlastZoneProcessorSal;
import com.concirrus.submissionAdapter.service.TenantService;
import com.concirrus.submissionAdapter.utils.SlackNotifier;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

import java.time.Instant;
import java.util.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("AggregationWorkflowService Tests")
class AggregationWorkflowServiceTest {

    @Mock
    private AggregationJobRepository jobRepository;

    @Mock
    private LocationRepository locationRepository;

    @Mock
    private LocationTaskRepository taskRepository;

    @Mock
    private AccountRepository accountRepository;

    @Mock
    private BlastZoneProcessorSal blastZoneProcessorSal;

    @Mock
    private EventSender eventSender;

    @Mock
    private TenantService tenantService;

    @Mock
    private SlackNotifier slackNotifier;

    @InjectMocks
    private AggregationWorkflowService aggregationWorkflowService;

    private AggregationJob testAggregationJob;
    private Location testLocation;
    private LocationTask testLocationTask;
    private Account testAccount;
    private TaskCallBack testTaskCallBack;

    @BeforeEach
    void setUp() {
        // Setup test aggregation job
        testAggregationJob = AggregationJob.builder()
                .jobId("JOB123")
                .submissionId("SUB123")
                .clientId("CLIENT123")
                .quoteId("QUOTE123")
                .updateType(UpdateType.QUOTE_CREATED)
                .status(ProcessingStatus.IN_PROGRESS)
                .countOfLocations(0)
                .createdAt(Instant.now())
                .miAnalysisJobIds(Arrays.asList("MI_JOB_1", "MI_JOB_2"))
                .build();

        // Setup test location
        testLocation = Location.builder()
                .id("LOC123")
                .submissionId("SUB123")
                .latitude(40.7128)
                .longitude(-74.0060)
                .state(State.IN_REVIEW)
                .locationName("Test Location")
                .build();

        // Setup test location task
        testLocationTask = LocationTask.builder()
                .taskId("TASK123")
                .jobId("JOB123")
                .locationId("LOC123")
                .status(ProcessingStatus.IN_PROGRESS)
                .updateType(UpdateType.QUOTE_CREATED)
                .createdOn(Instant.now())
                .build();

        // Setup test account
        testAccount = new Account();
        testAccount.setId("ACC123");
        testAccount.setSubmissionId("SUB123");
        testAccount.setQuoteId("QUOTE123");
        testAccount.setLimit(1000000.0);
        testAccount.setExcess(10000.0);
        testAccount.setWrittenLine(0.5);
        testAccount.setDeductible(5000.0);
        testAccount.setLineOfBusiness("POLITICAL_VIOLENCE");
        testAccount.setPerils(Arrays.asList("S&T"));
        testAccount.setState(State.IN_REVIEW);

        // Setup test task callback
        testTaskCallBack = TaskCallBack.builder()
                .jobId("JOB123")
                .locationId("LOC123")
                .taskId("TASK123")
                .status(ProcessingStatus.COMPLETED)
                .build();
    }

    @Nested
    @DisplayName("Create Tasks And Send Events Tests")
    class CreateTasksAndSendEventsTests {

        @Test
        @DisplayName("Should create tasks and send events for locations with specific location IDs")
        void shouldCreateTasksAndSendEventsForLocationsWithSpecificLocationIds() {
            // Given
            List<String> locationIds = Arrays.asList("LOC123", "LOC456");
            List<Location> locations = Arrays.asList(testLocation);
            Page<Location> locationPage = new PageImpl<>(locations);

            when(locationRepository.findByIdIn(eq(locationIds), any(Pageable.class)))
                    .thenReturn(locationPage)
                    .thenReturn(Page.empty());

            // When
            aggregationWorkflowService.createTasksAndSendEvents(testAggregationJob, UpdateType.QUOTE_CREATED, locationIds);

            // Then
            verify(locationRepository, times(2)).findByIdIn(eq(locationIds), any(Pageable.class));
            verify(eventSender).sendLocationEvents(any());
            verify(taskRepository).saveAll(any());
            assertEquals(1, testAggregationJob.getCountOfLocations());
        }

        @Test
        @DisplayName("Should create tasks and send events for all locations when location IDs are null")
        void shouldCreateTasksAndSendEventsForAllLocationsWhenLocationIdsAreNull() {
            // Given
            List<Location> locations = Arrays.asList(testLocation);
            Page<Location> locationPage = new PageImpl<>(locations);

            when(locationRepository.findBySubmissionId(eq("SUB123"), any(Pageable.class)))
                    .thenReturn(locationPage)
                    .thenReturn(Page.empty());

            // When
            aggregationWorkflowService.createTasksAndSendEvents(testAggregationJob, UpdateType.QUOTE_CREATED, null);

            // Then
            verify(locationRepository, times(2)).findBySubmissionId(eq("SUB123"), any(Pageable.class));
            verify(eventSender).sendLocationEvents(any());
            verify(taskRepository).saveAll(any());
            assertEquals(1, testAggregationJob.getCountOfLocations());
        }

        @Test
        @DisplayName("Should create tasks and send events for all locations when location IDs are empty")
        void shouldCreateTasksAndSendEventsForAllLocationsWhenLocationIdsAreEmpty() {
            // Given
            List<Location> locations = Arrays.asList(testLocation);
            Page<Location> locationPage = new PageImpl<>(locations);

            when(locationRepository.findBySubmissionId(eq("SUB123"), any(Pageable.class)))
                    .thenReturn(locationPage)
                    .thenReturn(Page.empty());

            // When
            aggregationWorkflowService.createTasksAndSendEvents(testAggregationJob, UpdateType.QUOTE_CREATED, Collections.emptyList());

            // Then
            verify(locationRepository, times(2)).findBySubmissionId(eq("SUB123"), any(Pageable.class));
            verify(eventSender).sendLocationEvents(any());
            verify(taskRepository).saveAll(any());
            assertEquals(1, testAggregationJob.getCountOfLocations());
        }

        @Test
        @DisplayName("Should handle multiple pages of locations")
        void shouldHandleMultiplePagesOfLocations() {
            // Given
            List<Location> firstPageLocations = Arrays.asList(testLocation);
            Location secondLocation = Location.builder()
                    .id("LOC456")
                    .submissionId("SUB123")
                    .latitude(41.8781)
                    .longitude(-87.6298)
                    .state(State.IN_REVIEW)
                    .build();
            List<Location> secondPageLocations = Arrays.asList(secondLocation);

            Page<Location> firstPage = new PageImpl<>(firstPageLocations);
            Page<Location> secondPage = new PageImpl<>(secondPageLocations);

            when(locationRepository.findBySubmissionId(eq("SUB123"), any(Pageable.class)))
                    .thenReturn(firstPage)
                    .thenReturn(secondPage)
                    .thenReturn(Page.empty());

            // When
            aggregationWorkflowService.createTasksAndSendEvents(testAggregationJob, UpdateType.QUOTE_CREATED, null);

            // Then
            verify(locationRepository, times(3)).findBySubmissionId(eq("SUB123"), any(Pageable.class));
            verify(eventSender, times(2)).sendLocationEvents(any());
            verify(taskRepository, times(2)).saveAll(any());
            assertEquals(2, testAggregationJob.getCountOfLocations());
        }

        @Test
        @DisplayName("Should handle empty location results")
        void shouldHandleEmptyLocationResults() {
            // Given
            when(locationRepository.findBySubmissionId(eq("SUB123"), any(Pageable.class)))
                    .thenReturn(Page.empty());

            // When
            aggregationWorkflowService.createTasksAndSendEvents(testAggregationJob, UpdateType.QUOTE_CREATED, null);

            // Then
            verify(locationRepository).findBySubmissionId(eq("SUB123"), any(Pageable.class));
            verifyNoInteractions(eventSender);
            verifyNoInteractions(taskRepository);
            assertEquals(0, testAggregationJob.getCountOfLocations());
        }

        @Test
        @DisplayName("Should create correct location tasks from locations")
        void shouldCreateCorrectLocationTasksFromLocations() {
            // Given
            List<Location> locations = Arrays.asList(testLocation);
            Page<Location> locationPage = new PageImpl<>(locations);

            when(locationRepository.findBySubmissionId(eq("SUB123"), any(Pageable.class)))
                    .thenReturn(locationPage)
                    .thenReturn(Page.empty());

            // When
            aggregationWorkflowService.createTasksAndSendEvents(testAggregationJob, UpdateType.QUOTE_CREATED, null);

            // Then
            ArgumentCaptor<List<LocationTask>> taskCaptor = ArgumentCaptor.forClass(List.class);
            verify(taskRepository).saveAll(taskCaptor.capture());

            List<LocationTask> savedTasks = taskCaptor.getValue();
            assertEquals(1, savedTasks.size());

            LocationTask savedTask = savedTasks.get(0);
            assertEquals("JOB123", savedTask.getJobId());
            assertEquals("LOC123", savedTask.getLocationId());
            assertEquals(ProcessingStatus.IN_PROGRESS, savedTask.getStatus());
            assertEquals(UpdateType.QUOTE_CREATED, savedTask.getUpdateType());
            assertNotNull(savedTask.getTaskId());
            assertNotNull(savedTask.getCreatedOn());
        }

        @Test
        @DisplayName("Should create correct location events from locations")
        void shouldCreateCorrectLocationEventsFromLocations() {
            // Given
            List<Location> locations = Arrays.asList(testLocation);
            Page<Location> locationPage = new PageImpl<>(locations);

            when(locationRepository.findBySubmissionId(eq("SUB123"), any(Pageable.class)))
                    .thenReturn(locationPage)
                    .thenReturn(Page.empty());

            // When
            aggregationWorkflowService.createTasksAndSendEvents(testAggregationJob, UpdateType.QUOTE_CREATED, null);

            // Then
            ArgumentCaptor<List<LocationEventDTO>> eventCaptor = ArgumentCaptor.forClass(List.class);
            verify(eventSender).sendLocationEvents(eventCaptor.capture());

            List<LocationEventDTO> sentEvents = eventCaptor.getValue();
            assertEquals(1, sentEvents.size());

            LocationEventDTO sentEvent = sentEvents.get(0);
            assertEquals("SUB123", sentEvent.getSubmissionId());
            assertEquals("LOC123", sentEvent.getLocationId());
            assertEquals(40.7128, sentEvent.getLatitude());
            assertEquals(-74.0060, sentEvent.getLongitude());
            assertEquals(State.IN_REVIEW, sentEvent.getState());
            assertEquals(UpdateType.QUOTE_CREATED, sentEvent.getUpdateType());
            assertEquals("JOB123", sentEvent.getJobId());
        }
    }

    @Nested
    @DisplayName("Handle Task Callback Tests")
    class HandleTaskCallbackTests {

        @Test
        @DisplayName("Should handle location-specific callback and update task status")
        void shouldHandleLocationSpecificCallbackAndUpdateTaskStatus() {
            // Given
            when(taskRepository.findByJobIdAndLocationId("JOB123", "LOC123"))
                    .thenReturn(Optional.of(testLocationTask));
            when(taskRepository.countByJobIdAndStatus("JOB123", ProcessingStatus.IN_PROGRESS))
                    .thenReturn(0L);
            when(jobRepository.findByJobId("JOB123"))
                    .thenReturn(Optional.of(testAggregationJob));

            // When
            aggregationWorkflowService.handleTaskCallback(testTaskCallBack);

            // Then
            verify(taskRepository).findByJobIdAndLocationId("JOB123", "LOC123");
            verify(taskRepository).save(testLocationTask);
            assertEquals(ProcessingStatus.COMPLETED, testLocationTask.getStatus());
            assertNotNull(testLocationTask.getUpdatedOn());

            verify(taskRepository).countByJobIdAndStatus("JOB123", ProcessingStatus.IN_PROGRESS);
            verify(jobRepository).findByJobId("JOB123");
            verify(jobRepository).save(testAggregationJob);
            assertEquals(ProcessingStatus.COMPLETED, testAggregationJob.getStatus());
        }

        @Test
        @DisplayName("Should handle code-level callback with completed status")
        void shouldHandleCodeLevelCallbackWithCompletedStatus() {
            // Given
            TaskCallBack codeLevelCallback = TaskCallBack.builder()
                    .jobId("JOB123")
                    .locationId(null)
                    .status(ProcessingStatus.COMPLETED)
                    .build();

            when(taskRepository.countByJobIdAndStatus("JOB123", ProcessingStatus.IN_PROGRESS))
                    .thenReturn(0L);
            when(jobRepository.findByJobId("JOB123"))
                    .thenReturn(Optional.of(testAggregationJob));

            // When
            aggregationWorkflowService.handleTaskCallback(codeLevelCallback);

            // Then
            verify(taskRepository, never()).findByJobIdAndLocationId(anyString(), anyString());
            verify(taskRepository, never()).save(any(LocationTask.class));

            verify(taskRepository).countByJobIdAndStatus("JOB123", ProcessingStatus.IN_PROGRESS);
            verify(jobRepository).findByJobId("JOB123");
            verify(jobRepository).save(testAggregationJob);
            assertEquals(ProcessingStatus.COMPLETED, testAggregationJob.getStatus());
        }

        @Test
        @DisplayName("Should handle code-level callback with empty location ID")
        void shouldHandleCodeLevelCallbackWithEmptyLocationId() {
            // Given
            TaskCallBack codeLevelCallback = TaskCallBack.builder()
                    .jobId("JOB123")
                    .locationId("")
                    .status(ProcessingStatus.COMPLETED)
                    .build();

            when(taskRepository.countByJobIdAndStatus("JOB123", ProcessingStatus.IN_PROGRESS))
                    .thenReturn(0L);
            when(jobRepository.findByJobId("JOB123"))
                    .thenReturn(Optional.of(testAggregationJob));

            // When
            aggregationWorkflowService.handleTaskCallback(codeLevelCallback);

            // Then
            verify(taskRepository, never()).findByJobIdAndLocationId(anyString(), anyString());
            verify(taskRepository, never()).save(any(LocationTask.class));

            verify(taskRepository).countByJobIdAndStatus("JOB123", ProcessingStatus.IN_PROGRESS);
            verify(jobRepository).save(testAggregationJob);
        }

        @Test
        @DisplayName("Should handle code-level callback with non-completed status")
        void shouldHandleCodeLevelCallbackWithNonCompletedStatus() {
            // Given
            TaskCallBack codeLevelCallback = TaskCallBack.builder()
                    .jobId("JOB123")
                    .locationId(null)
                    .status(ProcessingStatus.FAILED)
                    .build();

            // When
            aggregationWorkflowService.handleTaskCallback(codeLevelCallback);

            // Then
            verify(taskRepository, never()).findByJobIdAndLocationId(anyString(), anyString());
            verify(taskRepository, never()).save(any(LocationTask.class));
            verify(taskRepository, never()).countByJobIdAndStatus(anyString(), any(ProcessingStatus.class));
            verify(jobRepository, never()).findByJobId(anyString());
            verify(jobRepository, never()).save(any(AggregationJob.class));
        }

        @Test
        @DisplayName("Should throw RuntimeException when task not found")
        void shouldThrowRuntimeExceptionWhenTaskNotFound() {
            // Given
            when(taskRepository.findByJobIdAndLocationId("JOB123", "LOC123"))
                    .thenReturn(Optional.empty());

            // When & Then
            RuntimeException exception = assertThrows(RuntimeException.class, () ->
                    aggregationWorkflowService.handleTaskCallback(testTaskCallBack));

            assertEquals("Task not found for jobId: JOB123 and locationId: LOC123", exception.getMessage());
            verify(taskRepository).findByJobIdAndLocationId("JOB123", "LOC123");
            verify(taskRepository, never()).save(any(LocationTask.class));
        }

        @Test
        @DisplayName("Should not update job status when tasks are still in progress")
        void shouldNotUpdateJobStatusWhenTasksAreStillInProgress() {
            // Given
            when(taskRepository.findByJobIdAndLocationId("JOB123", "LOC123"))
                    .thenReturn(Optional.of(testLocationTask));
            when(taskRepository.countByJobIdAndStatus("JOB123", ProcessingStatus.IN_PROGRESS))
                    .thenReturn(2L);

            // When
            aggregationWorkflowService.handleTaskCallback(testTaskCallBack);

            // Then
            verify(taskRepository).save(testLocationTask);
            verify(taskRepository).countByJobIdAndStatus("JOB123", ProcessingStatus.IN_PROGRESS);
            verify(jobRepository, never()).findByJobId(anyString());
            verify(jobRepository, never()).save(any(AggregationJob.class));
        }

        @Test
        @DisplayName("Should throw RuntimeException when job not found during status update")
        void shouldThrowRuntimeExceptionWhenJobNotFoundDuringStatusUpdate() {
            // Given
            when(taskRepository.findByJobIdAndLocationId("JOB123", "LOC123"))
                    .thenReturn(Optional.of(testLocationTask));
            when(taskRepository.countByJobIdAndStatus("JOB123", ProcessingStatus.IN_PROGRESS))
                    .thenReturn(0L);
            when(jobRepository.findByJobId("JOB123"))
                    .thenReturn(Optional.empty());

            // When & Then
            RuntimeException exception = assertThrows(RuntimeException.class, () ->
                    aggregationWorkflowService.handleTaskCallback(testTaskCallBack));

            assertEquals("Job not found: JOB123", exception.getMessage());
            verify(taskRepository).save(testLocationTask);
            verify(jobRepository).findByJobId("JOB123");
            verify(jobRepository, never()).save(any(AggregationJob.class));
        }

        @Test
        @DisplayName("Should trigger MI analysis when job is completed")
        void shouldTriggerMiAnalysisWhenJobIsCompleted() {
            // Given
            when(taskRepository.findByJobIdAndLocationId("JOB123", "LOC123"))
                    .thenReturn(Optional.of(testLocationTask));
            when(taskRepository.countByJobIdAndStatus("JOB123", ProcessingStatus.IN_PROGRESS))
                    .thenReturn(0L);
            when(jobRepository.findByJobId("JOB123"))
                    .thenReturn(Optional.of(testAggregationJob));

            // When
            aggregationWorkflowService.handleTaskCallback(testTaskCallBack);

            // Then
            verify(blastZoneProcessorSal).triggerMiAnalysisJobs(Arrays.asList("MI_JOB_1", "MI_JOB_2"));
        }

        @Test
        @DisplayName("Should not trigger MI analysis when MI analysis job IDs are null")
        void shouldNotTriggerMiAnalysisWhenMiAnalysisJobIdsAreNull() {
            // Given
            testAggregationJob.setMiAnalysisJobIds(null);

            when(taskRepository.findByJobIdAndLocationId("JOB123", "LOC123"))
                    .thenReturn(Optional.of(testLocationTask));
            when(taskRepository.countByJobIdAndStatus("JOB123", ProcessingStatus.IN_PROGRESS))
                    .thenReturn(0L);
            when(jobRepository.findByJobId("JOB123"))
                    .thenReturn(Optional.of(testAggregationJob));

            // When
            aggregationWorkflowService.handleTaskCallback(testTaskCallBack);

            // Then
            verify(blastZoneProcessorSal, never()).triggerMiAnalysisJobs(any());
        }

        @Test
        @DisplayName("Should not trigger MI analysis when MI analysis job IDs are empty")
        void shouldNotTriggerMiAnalysisWhenMiAnalysisJobIdsAreEmpty() {
            // Given
            testAggregationJob.setMiAnalysisJobIds(Collections.emptyList());

            when(taskRepository.findByJobIdAndLocationId("JOB123", "LOC123"))
                    .thenReturn(Optional.of(testLocationTask));
            when(taskRepository.countByJobIdAndStatus("JOB123", ProcessingStatus.IN_PROGRESS))
                    .thenReturn(0L);
            when(jobRepository.findByJobId("JOB123"))
                    .thenReturn(Optional.of(testAggregationJob));

            // When
            aggregationWorkflowService.handleTaskCallback(testTaskCallBack);

            // Then
            verify(blastZoneProcessorSal, never()).triggerMiAnalysisJobs(any());
        }
    }

    @Nested
    @DisplayName("Create MI Analysis Job Tests")
    class CreateMiAnalysisJobTests {

        @Test
        @DisplayName("Should create MI analysis job for QUOTE_CREATED update type")
        void shouldCreateMiAnalysisJobForQuoteCreatedUpdateType() {
            // Given
            testAggregationJob.setUpdateType(UpdateType.QUOTE_CREATED);
            List<String> miJobIds = Arrays.asList("MI_JOB_1", "MI_JOB_2");

            when(accountRepository.findBySubmissionIdAndQuoteId("SUB123", "QUOTE123"))
                    .thenReturn(Optional.of(testAccount));
            when(blastZoneProcessorSal.createMiAnalysisJobs(any()))
                    .thenReturn(miJobIds);

            // When
            aggregationWorkflowService.createMiAnalysisJob(testAggregationJob);

            // Then
            verify(accountRepository).findBySubmissionIdAndQuoteId("SUB123", "QUOTE123");
            verify(accountRepository, never()).findBySubmissionId(anyString());

            ArgumentCaptor<List<MiAnalysisJobRequest>> requestCaptor = ArgumentCaptor.forClass(List.class);
            verify(blastZoneProcessorSal).createMiAnalysisJobs(requestCaptor.capture());

            List<MiAnalysisJobRequest> requests = requestCaptor.getValue();
            assertEquals(1, requests.size());

            MiAnalysisJobRequest request = requests.get(0);
            assertEquals("SUB123", request.getSubmissionId());
            assertEquals("QUOTE123", request.getQuoteId());
            assertEquals(1000000.0, request.getSAndT());
            assertEquals(Arrays.asList("sAndT"), request.getPerils());
            assertEquals(10000.0, request.getExcess());
            assertEquals(0.5, request.getLine());
            assertEquals(5000.0, request.getDeductible());
            assertEquals(false, request.getInitiateJob());

            verify(jobRepository).save(testAggregationJob);
            assertEquals(miJobIds, testAggregationJob.getMiAnalysisJobIds());
        }

        @Test
        @DisplayName("Should create MI analysis job for QUOTE_UPDATED update type")
        void shouldCreateMiAnalysisJobForQuoteUpdatedUpdateType() {
            // Given
            testAggregationJob.setUpdateType(UpdateType.QUOTE_UPDATED);
            List<String> miJobIds = Arrays.asList("MI_JOB_1");

            when(accountRepository.findBySubmissionIdAndQuoteId("SUB123", "QUOTE123"))
                    .thenReturn(Optional.of(testAccount));
            when(blastZoneProcessorSal.createMiAnalysisJobs(any()))
                    .thenReturn(miJobIds);

            // When
            aggregationWorkflowService.createMiAnalysisJob(testAggregationJob);

            // Then
            verify(accountRepository).findBySubmissionIdAndQuoteId("SUB123", "QUOTE123");
            verify(accountRepository, never()).findBySubmissionId(anyString());
            verify(blastZoneProcessorSal).createMiAnalysisJobs(any());
            verify(jobRepository).save(testAggregationJob);
            assertEquals(miJobIds, testAggregationJob.getMiAnalysisJobIds());
        }

        @Test
        @DisplayName("Should create MI analysis job for non-quote update types")
        void shouldCreateMiAnalysisJobForNonQuoteUpdateTypes() {
            // Given
            testAggregationJob.setUpdateType(UpdateType.LOCATION_CREATED);
            List<Account> accounts = Arrays.asList(testAccount);
            List<String> miJobIds = Arrays.asList("MI_JOB_1");

            when(accountRepository.findBySubmissionId("SUB123"))
                    .thenReturn(accounts);
            when(blastZoneProcessorSal.createMiAnalysisJobs(any()))
                    .thenReturn(miJobIds);

            // When
            aggregationWorkflowService.createMiAnalysisJob(testAggregationJob);

            // Then
            verify(accountRepository, never()).findBySubmissionIdAndQuoteId(anyString(), anyString());
            verify(accountRepository).findBySubmissionId("SUB123");
            verify(blastZoneProcessorSal).createMiAnalysisJobs(any());
            verify(jobRepository).save(testAggregationJob);
            assertEquals(miJobIds, testAggregationJob.getMiAnalysisJobIds());
        }

        @Test
        @DisplayName("Should skip MI analysis job creation when no accounts found for quote update")
        void shouldSkipMiAnalysisJobCreationWhenNoAccountsFoundForQuoteUpdate() {
            // Given
            testAggregationJob.setUpdateType(UpdateType.QUOTE_CREATED);

            when(accountRepository.findBySubmissionIdAndQuoteId("SUB123", "QUOTE123"))
                    .thenReturn(Optional.empty());

            // When
            aggregationWorkflowService.createMiAnalysisJob(testAggregationJob);

            // Then
            verify(accountRepository).findBySubmissionIdAndQuoteId("SUB123", "QUOTE123");
            verifyNoInteractions(blastZoneProcessorSal);
            verify(jobRepository, never()).save(any());
        }

        @Test
        @DisplayName("Should skip MI analysis job creation when no accounts found for submission")
        void shouldSkipMiAnalysisJobCreationWhenNoAccountsFoundForSubmission() {
            // Given
            testAggregationJob.setUpdateType(UpdateType.LOCATION_CREATED);

            when(accountRepository.findBySubmissionId("SUB123"))
                    .thenReturn(Collections.emptyList());

            // When
            aggregationWorkflowService.createMiAnalysisJob(testAggregationJob);

            // Then
            verify(accountRepository).findBySubmissionId("SUB123");
            verifyNoInteractions(blastZoneProcessorSal);
            verify(jobRepository, never()).save(any());
        }

        @Test
        @DisplayName("Should skip MI analysis job creation when no valid accounts found")
        void shouldSkipMiAnalysisJobCreationWhenNoValidAccountsFound() {
            // Given
            testAggregationJob.setUpdateType(UpdateType.LOCATION_CREATED);

            Account invalidAccount = new Account();
            invalidAccount.setId("ACC456");
            invalidAccount.setSubmissionId("SUB123");
            invalidAccount.setLineOfBusiness("OTHER_LOB");
            invalidAccount.setPerils(Arrays.asList("OTHER_PERIL"));
            invalidAccount.setState(State.BOUND);

            when(accountRepository.findBySubmissionId("SUB123"))
                    .thenReturn(Arrays.asList(invalidAccount));

            // When
            aggregationWorkflowService.createMiAnalysisJob(testAggregationJob);

            // Then
            verify(accountRepository).findBySubmissionId("SUB123");
            verifyNoInteractions(blastZoneProcessorSal);
            verify(jobRepository, never()).save(any());
        }

        @Test
        @DisplayName("Should filter out invalid accounts and process only valid ones")
        void shouldFilterOutInvalidAccountsAndProcessOnlyValidOnes() {
            // Given
            testAggregationJob.setUpdateType(UpdateType.LOCATION_CREATED);

            Account invalidAccount = new Account();
            invalidAccount.setId("ACC456");
            invalidAccount.setSubmissionId("SUB123");
            invalidAccount.setLineOfBusiness("OTHER_LOB");
            invalidAccount.setPerils(Arrays.asList("OTHER_PERIL"));
            invalidAccount.setState(State.BOUND);

            List<Account> accounts = Arrays.asList(testAccount, invalidAccount);
            List<String> miJobIds = Arrays.asList("MI_JOB_1");

            when(accountRepository.findBySubmissionId("SUB123"))
                    .thenReturn(accounts);
            when(blastZoneProcessorSal.createMiAnalysisJobs(any()))
                    .thenReturn(miJobIds);

            // When
            aggregationWorkflowService.createMiAnalysisJob(testAggregationJob);

            // Then
            ArgumentCaptor<List<MiAnalysisJobRequest>> requestCaptor = ArgumentCaptor.forClass(List.class);
            verify(blastZoneProcessorSal).createMiAnalysisJobs(requestCaptor.capture());

            List<MiAnalysisJobRequest> requests = requestCaptor.getValue();
            assertEquals(1, requests.size()); // Only valid account should be processed
            assertEquals("SUB123", requests.get(0).getSubmissionId());
            assertEquals("QUOTE123", requests.get(0).getQuoteId());
        }

        @Test
        @DisplayName("Should handle multiple valid accounts")
        void shouldHandleMultipleValidAccounts() {
            // Given
            testAggregationJob.setUpdateType(UpdateType.LOCATION_CREATED);

            Account secondAccount = new Account();
            secondAccount.setId("ACC456");
            secondAccount.setSubmissionId("SUB123");
            secondAccount.setQuoteId("QUOTE456");
            secondAccount.setLimit(2000000.0);
            secondAccount.setExcess(20000.0);
            secondAccount.setWrittenLine(0.75);
            secondAccount.setDeductible(10000.0);
            secondAccount.setLineOfBusiness("POLITICAL_VIOLENCE");
            secondAccount.setPerils(Arrays.asList("S&T"));
            secondAccount.setState(State.IN_REVIEW);

            List<Account> accounts = Arrays.asList(testAccount, secondAccount);
            List<String> miJobIds = Arrays.asList("MI_JOB_1", "MI_JOB_2");

            when(accountRepository.findBySubmissionId("SUB123"))
                    .thenReturn(accounts);
            when(blastZoneProcessorSal.createMiAnalysisJobs(any()))
                    .thenReturn(miJobIds);

            // When
            aggregationWorkflowService.createMiAnalysisJob(testAggregationJob);

            // Then
            ArgumentCaptor<List<MiAnalysisJobRequest>> requestCaptor = ArgumentCaptor.forClass(List.class);
            verify(blastZoneProcessorSal).createMiAnalysisJobs(requestCaptor.capture());

            List<MiAnalysisJobRequest> requests = requestCaptor.getValue();
            assertEquals(2, requests.size());

            // Verify first account request
            MiAnalysisJobRequest firstRequest = requests.get(0);
            assertEquals("SUB123", firstRequest.getSubmissionId());
            assertEquals("QUOTE123", firstRequest.getQuoteId());
            assertEquals(1000000.0, firstRequest.getSAndT());

            // Verify second account request
            MiAnalysisJobRequest secondRequest = requests.get(1);
            assertEquals("SUB123", secondRequest.getSubmissionId());
            assertEquals("QUOTE456", secondRequest.getQuoteId());
            assertEquals(2000000.0, secondRequest.getSAndT());

            verify(jobRepository).save(testAggregationJob);
            assertEquals(miJobIds, testAggregationJob.getMiAnalysisJobIds());
        }
    }

    @Nested
    @DisplayName("Edge Cases and Error Handling Tests")
    class EdgeCasesAndErrorHandlingTests {

        @Test
        @DisplayName("Should handle null aggregation job in createTasksAndSendEvents")
        void shouldHandleNullAggregationJobInCreateTasksAndSendEvents() {
            // When & Then
            assertDoesNotThrow(() ->
                aggregationWorkflowService.createTasksAndSendEvents(null, UpdateType.QUOTE_CREATED, null));
        }

        @Test
        @DisplayName("Should handle null update type in createTasksAndSendEvents")
        void shouldHandleNullUpdateTypeInCreateTasksAndSendEvents() {
            // Given
            when(locationRepository.findBySubmissionId(eq("SUB123"), any(Pageable.class)))
                    .thenReturn(Page.empty());

            // When & Then
            assertDoesNotThrow(() ->
                aggregationWorkflowService.createTasksAndSendEvents(testAggregationJob, null, null));
        }

        @Test
        @DisplayName("Should handle null callback in handleTaskCallback")
        void shouldHandleNullCallbackInHandleTaskCallback() {
            // When & Then
            assertThrows(NullPointerException.class, () ->
                aggregationWorkflowService.handleTaskCallback(null));
        }

        @Test
        @DisplayName("Should handle null job in createMiAnalysisJob")
        void shouldHandleNullJobInCreateMiAnalysisJob() {
            // When & Then
            assertThrows(NullPointerException.class, () ->
                aggregationWorkflowService.createMiAnalysisJob(null));
        }

        @Test
        @DisplayName("Should handle repository exception in createTasksAndSendEvents")
        void shouldHandleRepositoryExceptionInCreateTasksAndSendEvents() {
            // Given
            when(locationRepository.findBySubmissionId(eq("SUB123"), any(Pageable.class)))
                    .thenThrow(new RuntimeException("Database connection failed"));

            // When & Then
            assertThrows(RuntimeException.class, () ->
                aggregationWorkflowService.createTasksAndSendEvents(testAggregationJob, UpdateType.QUOTE_CREATED, null));
        }

        @Test
        @DisplayName("Should handle event sender exception in createTasksAndSendEvents")
        void shouldHandleEventSenderExceptionInCreateTasksAndSendEvents() {
            // Given
            List<Location> locations = Arrays.asList(testLocation);
            Page<Location> locationPage = new PageImpl<>(locations);

            when(locationRepository.findBySubmissionId(eq("SUB123"), any(Pageable.class)))
                    .thenReturn(locationPage)
                    .thenReturn(Page.empty());
            doThrow(new RuntimeException("Event sending failed"))
                    .when(eventSender).sendLocationEvents(any());

            // When & Then
            assertThrows(RuntimeException.class, () ->
                aggregationWorkflowService.createTasksAndSendEvents(testAggregationJob, UpdateType.QUOTE_CREATED, null));
        }

        @Test
        @DisplayName("Should handle task repository save exception in createTasksAndSendEvents")
        void shouldHandleTaskRepositorySaveExceptionInCreateTasksAndSendEvents() {
            // Given
            List<Location> locations = Arrays.asList(testLocation);
            Page<Location> locationPage = new PageImpl<>(locations);

            when(locationRepository.findBySubmissionId(eq("SUB123"), any(Pageable.class)))
                    .thenReturn(locationPage)
                    .thenReturn(Page.empty());
            when(taskRepository.saveAll(any()))
                    .thenThrow(new RuntimeException("Task save failed"));

            // When & Then
            assertThrows(RuntimeException.class, () ->
                aggregationWorkflowService.createTasksAndSendEvents(testAggregationJob, UpdateType.QUOTE_CREATED, null));
        }

        @Test
        @DisplayName("Should handle BlastZoneProcessorSal exception in createMiAnalysisJob")
        void shouldHandleBlastZoneProcessorSalExceptionInCreateMiAnalysisJob() {
            // Given
            testAggregationJob.setUpdateType(UpdateType.QUOTE_CREATED);

            when(accountRepository.findBySubmissionIdAndQuoteId("SUB123", "QUOTE123"))
                    .thenReturn(Optional.of(testAccount));
            when(blastZoneProcessorSal.createMiAnalysisJobs(any()))
                    .thenThrow(new RuntimeException("MI analysis job creation failed"));

            // When & Then
            assertThrows(RuntimeException.class, () ->
                aggregationWorkflowService.createMiAnalysisJob(testAggregationJob));
        }

        @Test
        @DisplayName("Should handle job repository save exception in createMiAnalysisJob")
        void shouldHandleJobRepositorySaveExceptionInCreateMiAnalysisJob() {

            when(tenantService.isValidTenantId("CLIENT1")).thenReturn(true);
            when(tenantService.getTenantAlias("CLIENT1")).thenReturn("tenant1");
            // Given
            testAggregationJob.setUpdateType(UpdateType.QUOTE_CREATED);
            List<String> miJobIds = Arrays.asList("MI_JOB_1");

            when(accountRepository.findBySubmissionIdAndQuoteId("SUB123", "QUOTE123"))
                    .thenReturn(Optional.of(testAccount));
            when(blastZoneProcessorSal.createMiAnalysisJobs(any()))
                    .thenReturn(miJobIds);
            when(jobRepository.save(testAggregationJob))
                    .thenThrow(new RuntimeException("Job save failed"));

            // When & Then
            assertThrows(RuntimeException.class, () ->
                aggregationWorkflowService.createMiAnalysisJob(testAggregationJob));
        }

        @Test
        @DisplayName("Should handle locations with null coordinates")
        void shouldHandleLocationsWithNullCoordinates() {
            // Given
            Location locationWithNullCoordinates = Location.builder()
                    .id("LOC456")
                    .submissionId("SUB123")
                    .latitude(null)
                    .longitude(null)
                    .state(State.IN_REVIEW)
                    .build();

            List<Location> locations = Arrays.asList(locationWithNullCoordinates);
            Page<Location> locationPage = new PageImpl<>(locations);

            when(locationRepository.findBySubmissionId(eq("SUB123"), any(Pageable.class)))
                    .thenReturn(locationPage)
                    .thenReturn(Page.empty());

            // When
            aggregationWorkflowService.createTasksAndSendEvents(testAggregationJob, UpdateType.QUOTE_CREATED, null);

            // Then
            ArgumentCaptor<List<LocationEventDTO>> eventCaptor = ArgumentCaptor.forClass(List.class);
            verify(eventSender).sendLocationEvents(eventCaptor.capture());

            List<LocationEventDTO> sentEvents = eventCaptor.getValue();
            assertEquals(1, sentEvents.size());

            LocationEventDTO sentEvent = sentEvents.get(0);
            assertEquals("LOC456", sentEvent.getLocationId());
            assertNull(sentEvent.getLatitude());
            assertNull(sentEvent.getLongitude());
        }

        @Test
        @DisplayName("Should handle accounts with null values")
        void shouldHandleAccountsWithNullValues() {
            // Given
            testAggregationJob.setUpdateType(UpdateType.QUOTE_CREATED);

            Account accountWithNulls = new Account();
            accountWithNulls.setId("ACC456");
            accountWithNulls.setSubmissionId("SUB123");
            accountWithNulls.setQuoteId("QUOTE123");
            accountWithNulls.setLimit(null);
            accountWithNulls.setExcess(null);
            accountWithNulls.setWrittenLine(null);
            accountWithNulls.setDeductible(null);
            accountWithNulls.setLineOfBusiness("POLITICAL_VIOLENCE");
            accountWithNulls.setPerils(Arrays.asList("S&T"));
            accountWithNulls.setState(State.IN_REVIEW);

            List<String> miJobIds = Arrays.asList("MI_JOB_1");

            when(accountRepository.findBySubmissionIdAndQuoteId("SUB123", "QUOTE123"))
                    .thenReturn(Optional.of(accountWithNulls));
            when(blastZoneProcessorSal.createMiAnalysisJobs(any()))
                    .thenReturn(miJobIds);

            // When
            aggregationWorkflowService.createMiAnalysisJob(testAggregationJob);

            // Then
            ArgumentCaptor<List<MiAnalysisJobRequest>> requestCaptor = ArgumentCaptor.forClass(List.class);
            verify(blastZoneProcessorSal).createMiAnalysisJobs(requestCaptor.capture());

            List<MiAnalysisJobRequest> requests = requestCaptor.getValue();
            assertEquals(1, requests.size());

            MiAnalysisJobRequest request = requests.get(0);
            assertEquals("SUB123", request.getSubmissionId());
            assertEquals("QUOTE123", request.getQuoteId());
            assertNull(request.getSAndT());
            assertNull(request.getExcess());
            assertNull(request.getLine());
            assertNull(request.getDeductible());
        }

        @Test
        @DisplayName("Should handle very large batches of locations")
        void shouldHandleVeryLargeBatchesOfLocations() {
            // Given
            List<Location> largeLocationList = new ArrayList<>();
            for (int i = 0; i < 250; i++) {
                largeLocationList.add(Location.builder()
                        .id("LOC" + i)
                        .submissionId("SUB123")
                        .latitude(40.0 + i * 0.001)
                        .longitude(-74.0 + i * 0.001)
                        .state(State.IN_REVIEW)
                        .build());
            }

            // Split into pages of 100
            List<Location> firstPage = largeLocationList.subList(0, 100);
            List<Location> secondPage = largeLocationList.subList(100, 200);
            List<Location> thirdPage = largeLocationList.subList(200, 250);

            when(locationRepository.findBySubmissionId(eq("SUB123"), any(Pageable.class)))
                    .thenReturn(new PageImpl<>(firstPage))
                    .thenReturn(new PageImpl<>(secondPage))
                    .thenReturn(new PageImpl<>(thirdPage))
                    .thenReturn(Page.empty());

            // When
            aggregationWorkflowService.createTasksAndSendEvents(testAggregationJob, UpdateType.QUOTE_CREATED, null);

            // Then
            verify(locationRepository, times(4)).findBySubmissionId(eq("SUB123"), any(Pageable.class));
            verify(eventSender, times(3)).sendLocationEvents(any());
            verify(taskRepository, times(3)).saveAll(any());
            assertEquals(250, testAggregationJob.getCountOfLocations());
        }
    }
}
