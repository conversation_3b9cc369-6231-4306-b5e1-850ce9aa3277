package com.concirrus.submissionAdapter.service;

import com.concirrus.submissionAdapter.dto.submission.ClientConfigResponse;
import com.concirrus.submissionAdapter.sal.AccessManagementSal;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("TenantServiceImpl Tests")
class TenantServiceImplTest {

    @Mock
    private AccessManagementSal accessManagementSal;

    @InjectMocks
    private TenantServiceImp tenantService;

    private String testClientId;
    private ClientConfigResponse testClientConfig;
    private String testToken;

    @BeforeEach
    void setUp() {
        testClientId = "CLIENT123";
        testToken = "Bearer test-token-12345";
        
        testClientConfig = new ClientConfigResponse();
        testClientConfig.setId("CONFIG123");
        testClientConfig.setClientId(testClientId);
        testClientConfig.setTenantName("test-tenant");
        testClientConfig.setClientName("Test Client");
        testClientConfig.setAuthUrl("https://auth.example.com");
        testClientConfig.setApiUrl("https://api.example.com");
        testClientConfig.setBaseUrl("https://base.example.com");
        testClientConfig.setClientAlertUrl("https://alerts.example.com");
    }

    @Nested
    @DisplayName("Get Tenant Alias Tests")
    class GetTenantAliasTests {

        @Test
        @DisplayName("Should successfully return tenant alias for valid client ID")
        void shouldSuccessfullyReturnTenantAliasForValidClientId() {
            // Given
            when(accessManagementSal.getClientConfigByClientId(testClientId))
                    .thenReturn(testClientConfig);

            // When
            String result = tenantService.getTenantAlias(testClientId);

            // Then
            assertEquals("test-tenant", result);
            verify(accessManagementSal).getClientConfigByClientId(testClientId);
        }

        @Test
        @DisplayName("Should return null when client config has null tenant name")
        void shouldReturnNullWhenClientConfigHasNullTenantName() {
            // Given
            testClientConfig.setTenantName(null);
            when(accessManagementSal.getClientConfigByClientId(testClientId))
                    .thenReturn(testClientConfig);

            // When
            String result = tenantService.getTenantAlias(testClientId);

            // Then
            assertNull(result);
            verify(accessManagementSal).getClientConfigByClientId(testClientId);
        }

        @Test
        @DisplayName("Should handle null client config response")
        void shouldHandleNullClientConfigResponse() {
            // Given
            when(accessManagementSal.getClientConfigByClientId(testClientId))
                    .thenReturn(null);

            // When & Then
            assertThrows(NullPointerException.class, () ->
                    tenantService.getTenantAlias(testClientId));
            
            verify(accessManagementSal).getClientConfigByClientId(testClientId);
        }

        @Test
        @DisplayName("Should handle exception from access management service")
        void shouldHandleExceptionFromAccessManagementService() {
            // Given
            RuntimeException serviceException = new RuntimeException("Service unavailable");
            when(accessManagementSal.getClientConfigByClientId(testClientId))
                    .thenThrow(serviceException);

            // When & Then
            RuntimeException exception = assertThrows(RuntimeException.class, () ->
                    tenantService.getTenantAlias(testClientId));
            
            assertEquals("Service unavailable", exception.getMessage());
            verify(accessManagementSal).getClientConfigByClientId(testClientId);
        }

        @Test
        @DisplayName("Should handle null client ID")
        void shouldHandleNullClientId() {
            // Given
            when(accessManagementSal.getClientConfigByClientId(null))
                    .thenReturn(testClientConfig);

            // When
            String result = tenantService.getTenantAlias(null);

            // Then
            assertEquals("test-tenant", result);
            verify(accessManagementSal).getClientConfigByClientId(null);
        }

        @Test
        @DisplayName("Should handle empty client ID")
        void shouldHandleEmptyClientId() {
            // Given
            String emptyClientId = "";
            when(accessManagementSal.getClientConfigByClientId(emptyClientId))
                    .thenReturn(testClientConfig);

            // When
            String result = tenantService.getTenantAlias(emptyClientId);

            // Then
            assertEquals("test-tenant", result);
            verify(accessManagementSal).getClientConfigByClientId(emptyClientId);
        }

        @Test
        @DisplayName("Should handle empty tenant name")
        void shouldHandleEmptyTenantName() {
            // Given
            testClientConfig.setTenantName("");
            when(accessManagementSal.getClientConfigByClientId(testClientId))
                    .thenReturn(testClientConfig);

            // When
            String result = tenantService.getTenantAlias(testClientId);

            // Then
            assertEquals("", result);
            verify(accessManagementSal).getClientConfigByClientId(testClientId);
        }
    }

    @Nested
    @DisplayName("Get Auth Token For Tenant Tests")
    class GetAuthTokenForTenantTests {

        @Test
        @DisplayName("Should successfully return auth token for valid client ID")
        void shouldSuccessfullyReturnAuthTokenForValidClientId() {
            // Given
            when(accessManagementSal.getToken(testClientId)).thenReturn(testToken);

            // When
            String result = tenantService.getAuthTokenForTenant(testClientId);

            // Then
            assertEquals(testToken, result);
            verify(accessManagementSal).getToken(testClientId);
        }

        @Test
        @DisplayName("Should return null when access management returns null token")
        void shouldReturnNullWhenAccessManagementReturnsNullToken() {
            // Given
            when(accessManagementSal.getToken(testClientId)).thenReturn(null);

            // When
            String result = tenantService.getAuthTokenForTenant(testClientId);

            // Then
            assertNull(result);
            verify(accessManagementSal).getToken(testClientId);
        }

        @Test
        @DisplayName("Should handle exception from access management service")
        void shouldHandleExceptionFromAccessManagementService() {
            // Given
            RuntimeException serviceException = new RuntimeException("Token service unavailable");
            when(accessManagementSal.getToken(testClientId)).thenThrow(serviceException);

            // When & Then
            RuntimeException exception = assertThrows(RuntimeException.class, () ->
                    tenantService.getAuthTokenForTenant(testClientId));
            
            assertEquals("Token service unavailable", exception.getMessage());
            verify(accessManagementSal).getToken(testClientId);
        }

        @Test
        @DisplayName("Should handle null client ID")
        void shouldHandleNullClientId() {
            // Given
            when(accessManagementSal.getToken(null)).thenReturn(testToken);

            // When
            String result = tenantService.getAuthTokenForTenant(null);

            // Then
            assertEquals(testToken, result);
            verify(accessManagementSal).getToken(null);
        }

        @Test
        @DisplayName("Should handle empty client ID")
        void shouldHandleEmptyClientId() {
            // Given
            String emptyClientId = "";
            when(accessManagementSal.getToken(emptyClientId)).thenReturn(testToken);

            // When
            String result = tenantService.getAuthTokenForTenant(emptyClientId);

            // Then
            assertEquals(testToken, result);
            verify(accessManagementSal).getToken(emptyClientId);
        }

        @Test
        @DisplayName("Should handle different token formats")
        void shouldHandleDifferentTokenFormats() {
            // Given
            String[] tokenFormats = {
                "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "simple-token-123",
                ""
            };

            for (String tokenFormat : tokenFormats) {
                when(accessManagementSal.getToken(testClientId)).thenReturn(tokenFormat);

                // When
                String result = tenantService.getAuthTokenForTenant(testClientId);

                // Then
                assertEquals(tokenFormat, result);
            }

            verify(accessManagementSal, times(tokenFormats.length)).getToken(testClientId);
        }
    }

    @Nested
    @DisplayName("Integration Tests")
    class IntegrationTests {

        @Test
        @DisplayName("Should handle both methods being called for same client")
        void shouldHandleBothMethodsBeingCalledForSameClient() {
            // Given
            when(accessManagementSal.getClientConfigByClientId(testClientId))
                    .thenReturn(testClientConfig);
            when(accessManagementSal.getToken(testClientId)).thenReturn(testToken);

            // When
            String tenantAlias = tenantService.getTenantAlias(testClientId);
            String authToken = tenantService.getAuthTokenForTenant(testClientId);

            // Then
            assertEquals("test-tenant", tenantAlias);
            assertEquals(testToken, authToken);
            verify(accessManagementSal).getClientConfigByClientId(testClientId);
            verify(accessManagementSal).getToken(testClientId);
        }

        @Test
        @DisplayName("Should handle different clients independently")
        void shouldHandleDifferentClientsIndependently() {
            // Given
            String client1 = "CLIENT1";
            String client2 = "CLIENT2";
            
            ClientConfigResponse config1 = new ClientConfigResponse();
            config1.setTenantName("tenant1");
            ClientConfigResponse config2 = new ClientConfigResponse();
            config2.setTenantName("tenant2");

            when(accessManagementSal.getClientConfigByClientId(client1)).thenReturn(config1);
            when(accessManagementSal.getClientConfigByClientId(client2)).thenReturn(config2);
            when(accessManagementSal.getToken(client1)).thenReturn("token1");
            when(accessManagementSal.getToken(client2)).thenReturn("token2");

            // When
            String alias1 = tenantService.getTenantAlias(client1);
            String alias2 = tenantService.getTenantAlias(client2);
            String token1 = tenantService.getAuthTokenForTenant(client1);
            String token2 = tenantService.getAuthTokenForTenant(client2);

            // Then
            assertEquals("tenant1", alias1);
            assertEquals("tenant2", alias2);
            assertEquals("token1", token1);
            assertEquals("token2", token2);
        }
    }
}
