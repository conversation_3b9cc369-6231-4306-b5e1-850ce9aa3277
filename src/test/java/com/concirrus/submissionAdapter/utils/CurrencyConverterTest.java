package com.concirrus.submissionAdapter.utils;

import com.concirrus.submissionAdapter.dto.currencyExchange.CurrencyRateApiResponse;
import com.concirrus.submissionAdapter.dto.currencyExchange.CurrencyRateData;
import com.concirrus.submissionAdapter.dto.currencyExchange.CurrencyRateRequest;
import com.concirrus.submissionAdapter.sal.CurrencyServiceSal;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("CurrencyConverter Tests")
class CurrencyConverterTest {

    @Mock
    private CurrencyServiceSal currencyServiceSal;

    @InjectMocks
    private CurrencyConverter currencyConverter;

    private CurrencyRateApiResponse testApiResponse;
    private List<CurrencyRateData> testRateData;
    private String testClientId;

    @BeforeEach
    void setUp() {
        testClientId = "CLIENT123";

        // Setup test currency rate data
        CurrencyRateData eurRate = new CurrencyRateData("EUR", "USD", "2024-01-01", 1.1);
        CurrencyRateData gbpRate = new CurrencyRateData("GBP", "USD", "2024-01-01", 1.3);
        CurrencyRateData jpyRate = new CurrencyRateData("JPY", "USD", "2024-01-01", 0.0067);
        
        testRateData = Arrays.asList(eurRate, gbpRate, jpyRate);
        
        testApiResponse = new CurrencyRateApiResponse();
        testApiResponse.setData(testRateData);
        testApiResponse.setSuccess(true);
        testApiResponse.setMessage("Success");
        testApiResponse.setTimestamp("2024-01-01T10:00:00Z");
    }

    @Nested
    @DisplayName("Get Exchange Rate Tests")
    class GetExchangeRateTests {

        @Test
        @DisplayName("Should return 1.0 for USD currency")
        void shouldReturn1ForUsdCurrency() {
            // When
            Double result = currencyConverter.getExchangeRate("USD", testClientId);

            // Then
            assertEquals(1.0, result);
            verifyNoInteractions(currencyServiceSal);
        }

        @Test
        @DisplayName("Should return 1.0 for null currency")
        void shouldReturn1ForNullCurrency() {
            // When
            Double result = currencyConverter.getExchangeRate(null, testClientId);

            // Then
            assertEquals(1.0, result);
            verifyNoInteractions(currencyServiceSal);
        }

        @Test
        @DisplayName("Should return 1.0 for USD case insensitive")
        void shouldReturn1ForUsdCaseInsensitive() {
            // When
            Double usdLower = currencyConverter.getExchangeRate("usd", testClientId);
            Double usdUpper = currencyConverter.getExchangeRate("USD", testClientId);
            Double usdMixed = currencyConverter.getExchangeRate("Usd", testClientId);

            // Then
            assertEquals(1.0, usdLower);
            assertEquals(1.0, usdUpper);
            assertEquals(1.0, usdMixed);
            verifyNoInteractions(currencyServiceSal);
        }

        @Test
        @DisplayName("Should successfully get exchange rate for non-USD currency")
        void shouldSuccessfullyGetExchangeRateForNonUsdCurrency() {
            // Given
            CurrencyRateApiResponse singleRateResponse = new CurrencyRateApiResponse();
            CurrencyRateData eurData = new CurrencyRateData("EUR", "USD", "2024-01-01", 1.1);
            singleRateResponse.setData(List.of(eurData));
            singleRateResponse.setSuccess(true);
            singleRateResponse.setMessage("Success");
            singleRateResponse.setTimestamp("2024-01-01T10:00:00Z");
            
            when(currencyServiceSal.getCurrencyRates(anyList(), anyString())).thenReturn(singleRateResponse);

            // When
            Double result = currencyConverter.getExchangeRate("EUR",testClientId);

            // Then
            assertEquals(1.1, result);
            
            ArgumentCaptor<List<CurrencyRateRequest>> requestCaptor = ArgumentCaptor.forClass(List.class);
            verify(currencyServiceSal).getCurrencyRates(requestCaptor.capture(), anyString());
            
            List<CurrencyRateRequest> capturedRequests = requestCaptor.getValue();
            assertEquals(1, capturedRequests.size());
            assertEquals("EUR", capturedRequests.getFirst().getFromCcy());
            assertEquals("USD", capturedRequests.getFirst().getToCcy());
        }

        @Test
        @DisplayName("Should return 0.0 when service throws exception")
        void shouldReturn0WhenServiceThrowsException() {
            // Given
            when(currencyServiceSal.getCurrencyRates(anyList(), eq("")))
                    .thenThrow(new RuntimeException("Service unavailable"));

            // When
            Double result = currencyConverter.getExchangeRate("EUR",testClientId);

            // Then
            assertEquals(0.0, result);
        }

        @Test
        @DisplayName("Should return 0.0 when response data is empty")
        void shouldReturn0WhenResponseDataIsEmpty() {
            // Given
            CurrencyRateApiResponse emptyResponse = new CurrencyRateApiResponse();
            emptyResponse.setData(Collections.emptyList());
            emptyResponse.setSuccess(true);
            emptyResponse.setMessage("Success");
            emptyResponse.setTimestamp("2024-01-01T10:00:00Z");
            
            when(currencyServiceSal.getCurrencyRates(anyList(), anyString())).thenReturn(emptyResponse);

            // When & Then
            assertEquals(0.0, currencyConverter.getExchangeRate("EUR",testClientId));
        }

        @Test
        @DisplayName("Should handle null response from service")
        void shouldHandleNullResponseFromService() {
            // Given
            when(currencyServiceSal.getCurrencyRates(anyList(), eq(""))).thenReturn(null);

            // When
            Double result = currencyConverter.getExchangeRate("EUR",testClientId);

            // Then
            assertEquals(0.0, result);
        }

        @Test
        @DisplayName("Should handle response with null data")
        void shouldHandleResponseWithNullData() {
            // Given
            CurrencyRateApiResponse nullDataResponse = new CurrencyRateApiResponse();
            nullDataResponse.setData(null);
            nullDataResponse.setSuccess(true);
            nullDataResponse.setMessage("Success");
            nullDataResponse.setTimestamp("2024-01-01T10:00:00Z");
            
            when(currencyServiceSal.getCurrencyRates(anyList(), anyString())).thenReturn(nullDataResponse);

            // When
            Double result = currencyConverter.getExchangeRate("EUR",testClientId);

            // Then
            assertEquals(0.0, result);
        }

        @Test
        @DisplayName("Should handle different currency codes")
        void shouldHandleDifferentCurrencyCodes() {
            // Given
            Map<String, Double> expectedRates = Map.of(
                    "EUR", 1.1,
                    "GBP", 1.3,
                    "JPY", 0.0067,
                    "CAD", 0.75,
                    "AUD", 0.68
            );

            expectedRates.forEach((currency, rate) -> {
                CurrencyRateApiResponse response = new CurrencyRateApiResponse();
                CurrencyRateData data = new CurrencyRateData(currency, "USD", "2024-01-01", rate);
                response.setData(List.of(data));

                when(currencyServiceSal.getCurrencyRates(
                        argThat(requests ->requests != null && requests.size() == 1 && requests.get(0).getFromCcy().equals(currency)),
                       anyString()
                )).thenReturn(response);
            });

            // When & Then
            expectedRates.forEach((currency, expectedRate) -> {
                Double actualRate = currencyConverter.getExchangeRate(currency,testClientId);
                assertEquals(expectedRate, actualRate, 0.0001, "Rate mismatch for currency: " + currency);
            });

            // Verify all currencies were processed
            verify(currencyServiceSal, times(expectedRates.size())).getCurrencyRates(anyList(), anyString());
        }
    }

    @Nested
    @DisplayName("Get Exchange Rates Tests")
    class GetExchangeRatesTests {

        @Test
        @DisplayName("Should successfully get multiple exchange rates")
        void shouldSuccessfullyGetMultipleExchangeRates() {
            // Given
            List<String> currencies = Arrays.asList("EUR", "GBP", "JPY");
            when(currencyServiceSal.getCurrencyRates(anyList(), eq(testClientId))).thenReturn(testApiResponse);

            // When
            Map<String, Double> result = currencyConverter.getExchangeRates(currencies, testClientId);

            // Then
            assertEquals(3, result.size());
            assertEquals(1.1, result.get("EUR"));
            assertEquals(1.3, result.get("GBP"));
            assertEquals(0.0067, result.get("JPY"));
            
            ArgumentCaptor<List<CurrencyRateRequest>> requestCaptor = ArgumentCaptor.forClass(List.class);
            verify(currencyServiceSal).getCurrencyRates(requestCaptor.capture(), eq(testClientId));
            
            List<CurrencyRateRequest> capturedRequests = requestCaptor.getValue();
            assertEquals(3, capturedRequests.size());
            assertTrue(capturedRequests.stream().allMatch(req -> "USD".equals(req.getToCcy())));
        }

        @Test
        @DisplayName("Should return empty map when service throws exception")
        void shouldReturnEmptyMapWhenServiceThrowsException() {
            // Given
            List<String> currencies = Arrays.asList("EUR", "GBP");
            when(currencyServiceSal.getCurrencyRates(anyList(), eq(testClientId)))
                    .thenThrow(new RuntimeException("Service error"));

            // When
            Map<String, Double> result = currencyConverter.getExchangeRates(currencies, testClientId);

            // Then
            assertTrue(result.isEmpty());
        }

        @Test
        @DisplayName("Should handle empty currency list")
        void shouldHandleEmptyCurrencyList() {
            // Given
            List<String> emptyCurrencies = Collections.emptyList();
            CurrencyRateApiResponse emptyResponse = new CurrencyRateApiResponse();
            emptyResponse.setData(Collections.emptyList());

            // When
            Map<String, Double> result = currencyConverter.getExchangeRates(emptyCurrencies, testClientId);

            // Then
            assertTrue(result.isEmpty());
        }

        @Test
        @DisplayName("Should handle single currency in list")
        void shouldHandleSingleCurrencyInList() {
            // Given
            List<String> singleCurrency = Arrays.asList("EUR");
            CurrencyRateApiResponse singleResponse = new CurrencyRateApiResponse();
            CurrencyRateData eurData = new CurrencyRateData("EUR", "USD", "2024-01-01", 1.1);
            singleResponse.setData(Arrays.asList(eurData));
            
            when(currencyServiceSal.getCurrencyRates(anyList(), eq(testClientId))).thenReturn(singleResponse);

            // When
            Map<String, Double> result = currencyConverter.getExchangeRates(singleCurrency, testClientId);

            // Then
            assertEquals(1, result.size());
            assertEquals(1.1, result.get("EUR"));
        }

        @Test
        @DisplayName("Should handle null currency list")
        void shouldHandleNullCurrencyList() {
            // When & Then
            assertEquals(Map.of(), currencyConverter.getExchangeRates(null, testClientId));

        }

        @Test
        @DisplayName("Should handle null client ID")
        void shouldHandleNullClientId() {
            // Given
            List<String> currencies = Arrays.asList("EUR");
            when(currencyServiceSal.getCurrencyRates(anyList(), isNull())).thenReturn(testApiResponse);

            // When
            Map<String, Double> result = currencyConverter.getExchangeRates(currencies, null);

            // Then
            assertFalse(result.isEmpty());
            verify(currencyServiceSal).getCurrencyRates(anyList(), isNull());
        }

        @Test
        @DisplayName("Should handle response with null data list")
        void shouldHandleResponseWithNullDataList() {
            // Given
            List<String> currencies = Arrays.asList("EUR");
            CurrencyRateApiResponse nullDataResponse = new CurrencyRateApiResponse();
            nullDataResponse.setData(null);
            
            when(currencyServiceSal.getCurrencyRates(anyList(), eq(testClientId))).thenReturn(nullDataResponse);

            // When
            Map<String, Double> result = currencyConverter.getExchangeRates(currencies, testClientId);

            // Then
            assertEquals(Collections.emptyMap(), result);
        }

        @Test
        @DisplayName("Should create correct currency rate requests")
        void shouldCreateCorrectCurrencyRateRequests() {
            // Given
            List<String> currencies = Arrays.asList("EUR", "GBP");
            when(currencyServiceSal.getCurrencyRates(anyList(), eq(testClientId))).thenReturn(testApiResponse);

            // When
            currencyConverter.getExchangeRates(currencies, testClientId);

            // Then
            ArgumentCaptor<List<CurrencyRateRequest>> requestCaptor = ArgumentCaptor.forClass(List.class);
            verify(currencyServiceSal).getCurrencyRates(requestCaptor.capture(), eq(testClientId));
            
            List<CurrencyRateRequest> requests = requestCaptor.getValue();
            assertEquals(2, requests.size());
            
            CurrencyRateRequest eurRequest = requests.stream()
                    .filter(req -> "EUR".equals(req.getFromCcy()))
                    .findFirst()
                    .orElseThrow();
            assertEquals("EUR", eurRequest.getFromCcy());
            assertEquals("USD", eurRequest.getToCcy());
            assertNotNull(eurRequest.getAsOfDate());
            
            CurrencyRateRequest gbpRequest = requests.stream()
                    .filter(req -> "GBP".equals(req.getFromCcy()))
                    .findFirst()
                    .orElseThrow();
            assertEquals("GBP", gbpRequest.getFromCcy());
            assertEquals("USD", gbpRequest.getToCcy());
            assertNotNull(gbpRequest.getAsOfDate());
        }
    }
}
