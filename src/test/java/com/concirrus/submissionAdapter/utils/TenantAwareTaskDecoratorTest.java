package com.concirrus.submissionAdapter.utils;

import com.concirrus.submissionAdapter.config.TenantContextHolder;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicReference;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("TenantAwareTaskDecorator Tests")
class TenantAwareTaskDecoratorTest {

    private TenantAwareTaskDecorator taskDecorator;
    private String testTenantId;
    private String testTenantAlias;

    @BeforeEach
    void setUp() {
        taskDecorator = new TenantAwareTaskDecorator();
        testTenantId = "TENANT123";
        testTenantAlias = "test-tenant-alias";
        
        // Clear any existing tenant context
        TenantContextHolder.clearTenantId();
        TenantContextHolder.clearTenantAlias();
    }

    @AfterEach
    void tearDown() {
        // Clean up tenant context after each test
        TenantContextHolder.clearTenantId();
        TenantContextHolder.clearTenantAlias();
    }

    @Nested
    @DisplayName("Decorate Method Tests")
    class DecorateMethodTests {

        @Test
        @DisplayName("Should capture and propagate tenant context to async thread")
        void shouldCaptureAndPropagateTenantContextToAsyncThread() throws InterruptedException {
            // Given
            TenantContextHolder.setTenantContext(testTenantId, testTenantAlias);
            
            CountDownLatch latch = new CountDownLatch(1);
            AtomicReference<String> capturedTenantId = new AtomicReference<>();
            AtomicReference<String> capturedTenantAlias = new AtomicReference<>();
            
            Runnable originalTask = () -> {
                capturedTenantId.set(TenantContextHolder.getTenantId());
                capturedTenantAlias.set(TenantContextHolder.getTenantAlias());
                latch.countDown();
            };

            // When
            Runnable decoratedTask = taskDecorator.decorate(originalTask);
            
            // Clear context in main thread to simulate different thread
            TenantContextHolder.clearTenantId();
            TenantContextHolder.clearTenantAlias();
            
            ExecutorService executor = Executors.newSingleThreadExecutor();
            executor.submit(decoratedTask);
            
            // Then
            assertTrue(latch.await(1, TimeUnit.SECONDS));
            assertEquals(testTenantId, capturedTenantId.get());
            assertEquals(testTenantAlias, capturedTenantAlias.get());
            
            executor.shutdown();
        }

        @Test
        @DisplayName("Should handle null tenant context gracefully")
        void shouldHandleNullTenantContextGracefully() throws InterruptedException {
            // Given - No tenant context set
            CountDownLatch latch = new CountDownLatch(1);
            AtomicReference<String> capturedTenantId = new AtomicReference<>();
            AtomicReference<String> capturedTenantAlias = new AtomicReference<>();
            
            Runnable originalTask = () -> {
                capturedTenantId.set(TenantContextHolder.getTenantId());
                capturedTenantAlias.set(TenantContextHolder.getTenantAlias());
                latch.countDown();
            };

            // When
            Runnable decoratedTask = taskDecorator.decorate(originalTask);
            
            ExecutorService executor = Executors.newSingleThreadExecutor();
            executor.submit(decoratedTask);
            
            // Then
            assertTrue(latch.await(1, TimeUnit.SECONDS));
            assertNull(capturedTenantId.get());
            assertNull(capturedTenantAlias.get());
            
            executor.shutdown();
        }

        @Test
        @DisplayName("Should handle null tenant ID with valid tenant alias")
        void shouldHandleNullTenantIdWithValidTenantAlias() throws InterruptedException {
            // Given
            TenantContextHolder.setTenantAlias(testTenantAlias);
            // TenantId remains null
            
            CountDownLatch latch = new CountDownLatch(1);
            AtomicReference<String> capturedTenantId = new AtomicReference<>();
            AtomicReference<String> capturedTenantAlias = new AtomicReference<>();
            
            Runnable originalTask = () -> {
                capturedTenantId.set(TenantContextHolder.getTenantId());
                capturedTenantAlias.set(TenantContextHolder.getTenantAlias());
                latch.countDown();
            };

            // When
            Runnable decoratedTask = taskDecorator.decorate(originalTask);
            
            ExecutorService executor = Executors.newSingleThreadExecutor();
            executor.submit(decoratedTask);
            
            // Then
            assertTrue(latch.await(1, TimeUnit.SECONDS));
            assertNull(capturedTenantId.get()); // Should not set context when tenantId is null
            assertNull(capturedTenantAlias.get()); // Should not set context when tenantId is null
            
            executor.shutdown();
        }

        @Test
        @DisplayName("Should always clean up tenant context after task execution")
        void shouldAlwaysCleanUpTenantContextAfterTaskExecution() throws InterruptedException {
            // Given
            TenantContextHolder.setTenantContext(testTenantId, testTenantAlias);
            
            CountDownLatch latch = new CountDownLatch(1);
            AtomicReference<String> tenantIdAfterCleanup = new AtomicReference<>();
            
            Runnable originalTask = () -> {
                // Task execution
                latch.countDown();
            };

            // When
            Runnable decoratedTask = taskDecorator.decorate(originalTask);
            
            ExecutorService executor = Executors.newSingleThreadExecutor();
            executor.submit(() -> {
                decoratedTask.run();
                // Check tenant context after decorated task completes
                tenantIdAfterCleanup.set(TenantContextHolder.getTenantId());
            });
            
            // Then
            assertTrue(latch.await(1, TimeUnit.SECONDS));
            Thread.sleep(100); // Give time for cleanup
            assertNull(tenantIdAfterCleanup.get());
            
            executor.shutdown();
        }

        @Test
        @DisplayName("Should clean up context even when task throws exception")
        void shouldCleanUpContextEvenWhenTaskThrowsException() throws InterruptedException {
            // Given
            TenantContextHolder.setTenantContext(testTenantId, testTenantAlias);
            
            CountDownLatch latch = new CountDownLatch(1);
            AtomicReference<String> tenantIdAfterException = new AtomicReference<>();
            
            Runnable originalTask = () -> {
                latch.countDown();
                throw new RuntimeException("Task failed");
            };

            // When
            Runnable decoratedTask = taskDecorator.decorate(originalTask);
            
            ExecutorService executor = Executors.newSingleThreadExecutor();
            executor.submit(() -> {
                try {
                    decoratedTask.run();
                } catch (RuntimeException e) {
                    // Expected exception
                }
                // Check tenant context after exception
                tenantIdAfterException.set(TenantContextHolder.getTenantId());
            });
            
            // Then
            assertTrue(latch.await(1, TimeUnit.SECONDS));
            Thread.sleep(100); // Give time for cleanup
            assertNull(tenantIdAfterException.get());
            
            executor.shutdown();
        }

        @Test
        @DisplayName("Should preserve original task execution")
        void shouldPreserveOriginalTaskExecution() throws InterruptedException {
            // Given
            TenantContextHolder.setTenantContext(testTenantId, testTenantAlias);
            
            CountDownLatch latch = new CountDownLatch(1);
            AtomicReference<String> executionResult = new AtomicReference<>();
            
            Runnable originalTask = () -> {
                executionResult.set("Task executed successfully");
                latch.countDown();
            };

            // When
            Runnable decoratedTask = taskDecorator.decorate(originalTask);
            
            ExecutorService executor = Executors.newSingleThreadExecutor();
            executor.submit(decoratedTask);
            
            // Then
            assertTrue(latch.await(1, TimeUnit.SECONDS));
            assertEquals("Task executed successfully", executionResult.get());
            
            executor.shutdown();
        }

        @Test
        @DisplayName("Should handle multiple concurrent decorated tasks")
        void shouldHandleMultipleConcurrentDecoratedTasks() throws InterruptedException {
            // Given
            int numberOfTasks = 5;
            CountDownLatch latch = new CountDownLatch(numberOfTasks);
            AtomicReference<String>[] capturedTenantIds = new AtomicReference[numberOfTasks];
            
            for (int i = 0; i < numberOfTasks; i++) {
                capturedTenantIds[i] = new AtomicReference<>();
            }
            
            TenantContextHolder.setTenantContext(testTenantId, testTenantAlias);
            
            ExecutorService executor = Executors.newFixedThreadPool(numberOfTasks);
            
            // When
            for (int i = 0; i < numberOfTasks; i++) {
                final int taskIndex = i;
                Runnable originalTask = () -> {
                    capturedTenantIds[taskIndex].set(TenantContextHolder.getTenantId());
                    latch.countDown();
                };
                
                Runnable decoratedTask = taskDecorator.decorate(originalTask);
                executor.submit(decoratedTask);
            }
            
            // Then
            assertTrue(latch.await(2, TimeUnit.SECONDS));
            
            for (int i = 0; i < numberOfTasks; i++) {
                assertEquals(testTenantId, capturedTenantIds[i].get(),
                        "Task " + i + " should have correct tenant ID");
            }
            
            executor.shutdown();
        }

        @Test
        @DisplayName("Should not interfere with main thread context")
        void shouldNotInterfereWithMainThreadContext() throws InterruptedException {
            // Given
            String mainThreadTenantId = "MAIN_TENANT";
            String asyncThreadTenantId = "ASYNC_TENANT";
            
            TenantContextHolder.setTenantContext(asyncThreadTenantId, "async-alias");
            
            CountDownLatch latch = new CountDownLatch(1);
            
            Runnable originalTask = () -> {
                // This should have the async tenant context
                assertEquals(asyncThreadTenantId, TenantContextHolder.getTenantId());
                latch.countDown();
            };

            // When
            Runnable decoratedTask = taskDecorator.decorate(originalTask);
            
            // Change main thread context
            TenantContextHolder.setTenantContext(mainThreadTenantId, "main-alias");
            
            ExecutorService executor = Executors.newSingleThreadExecutor();
            executor.submit(decoratedTask);
            
            // Then
            assertTrue(latch.await(1, TimeUnit.SECONDS));
            assertEquals(mainThreadTenantId, TenantContextHolder.getTenantId()); // Main thread should be unchanged
            
            executor.shutdown();
        }
    }

    @Nested
    @DisplayName("Edge Cases and Error Handling Tests")
    class EdgeCasesAndErrorHandlingTests {

        @Test
        @DisplayName("Should handle empty string tenant values")
        void shouldHandleEmptyStringTenantValues() throws InterruptedException {
            // Given
            TenantContextHolder.setTenantContext("", "");
            
            CountDownLatch latch = new CountDownLatch(1);
            AtomicReference<String> capturedTenantId = new AtomicReference<>();
            
            Runnable originalTask = () -> {
                capturedTenantId.set(TenantContextHolder.getTenantId());
                latch.countDown();
            };

            // When
            Runnable decoratedTask = taskDecorator.decorate(originalTask);
            
            ExecutorService executor = Executors.newSingleThreadExecutor();
            executor.submit(decoratedTask);
            
            // Then
            assertTrue(latch.await(1, TimeUnit.SECONDS));
            assertEquals("", capturedTenantId.get());
            
            executor.shutdown();
        }

        @Test
        @DisplayName("Should clean up worker thread context after task modifies it")
        void shouldCleanUpWorkerThreadContext() throws Exception {
            // Given
            TenantContextHolder.setTenantContext(testTenantId, testTenantAlias);

            AtomicReference<String> contextDuringTask = new AtomicReference<>();
            AtomicReference<String> contextAfterTask = new AtomicReference<>();
            CountDownLatch latch = new CountDownLatch(1);

            Runnable originalTask = () -> {
                // Task modifies tenant context
                TenantContextHolder.setTenantContext("MODIFIED_TENANT", "modified-alias");
                contextDuringTask.set(TenantContextHolder.getTenantId());
                latch.countDown();
            };

            // When
            Runnable decoratedTask = taskDecorator.decorate(originalTask);

            try (ExecutorService executor = Executors.newSingleThreadExecutor()) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    decoratedTask.run();
                    // Check context after decorator cleanup
                    contextAfterTask.set(TenantContextHolder.getTenantId());
                }, executor);

                future.get(2, TimeUnit.SECONDS);

                // Then
                assertTrue(latch.await(1, TimeUnit.SECONDS));
                assertEquals("MODIFIED_TENANT", contextDuringTask.get());
                assertNull(contextAfterTask.get()); // This should be null after cleanup

                // Main thread context should be unchanged
                assertEquals(testTenantId, TenantContextHolder.getTenantId());
            }
        }

        @Test
        @DisplayName("Should isolate context modifications between threads")
        void shouldIsolateContextModificationsBetweenThreads() throws Exception {
            // Given
            TenantContextHolder.setTenantContext(testTenantId, testTenantAlias);

            CountDownLatch latch = new CountDownLatch(1);

            Runnable originalTask = () -> {
                // Verify worker thread got the copied context
                assertEquals(testTenantId, TenantContextHolder.getTenantId());

                // Task modifies tenant context
                TenantContextHolder.setTenantContext("MODIFIED_TENANT", "modified-alias");
                assertEquals("MODIFIED_TENANT", TenantContextHolder.getTenantId());

                latch.countDown();
            };

            // When
            Runnable decoratedTask = taskDecorator.decorate(originalTask);

            try (ExecutorService executor = Executors.newSingleThreadExecutor()) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(decoratedTask, executor);
                future.get(2, TimeUnit.SECONDS);

                // Then
                assertTrue(latch.await(1, TimeUnit.SECONDS));

                // Main thread context should remain unchanged
                assertEquals(testTenantId, TenantContextHolder.getTenantId());
            }
        }

        @Test
        @DisplayName("Should handle interrupted thread during task execution")
        void shouldHandleInterruptedThreadDuringTaskExecution() throws InterruptedException {
            // Given
            TenantContextHolder.setTenantContext(testTenantId, testTenantAlias);
            
            CountDownLatch startLatch = new CountDownLatch(1);
            CountDownLatch interruptLatch = new CountDownLatch(1);
            
            Runnable originalTask = () -> {
                startLatch.countDown();
                try {
                    Thread.sleep(5000); // Long running task
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    interruptLatch.countDown();
                }
            };

            // When
            Runnable decoratedTask = taskDecorator.decorate(originalTask);
            
            ExecutorService executor = Executors.newSingleThreadExecutor();
            var future = executor.submit(decoratedTask);
            
            assertTrue(startLatch.await(1, TimeUnit.SECONDS));
            future.cancel(true); // Interrupt the task
            
            // Then
            assertTrue(interruptLatch.await(1, TimeUnit.SECONDS));
            
            executor.shutdown();
        }
    }
}
