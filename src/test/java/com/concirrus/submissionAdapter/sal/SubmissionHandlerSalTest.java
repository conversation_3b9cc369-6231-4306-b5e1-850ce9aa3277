package com.concirrus.submissionAdapter.sal;

import com.concirrus.submissionAdapter.dto.submission.BasicResponse;
import com.concirrus.submissionAdapter.dto.submission.InsuredAssetsResponse;
import com.concirrus.submissionAdapter.dto.submission.Submission;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
@ExtendWith(MockitoExtension.class)
@DisplayName("SubmissionHandlerSal Tests")
class SubmissionHandlerSalTest {

    private AccessManagementSal accessManagementSal;
    private RestTemplate restTemplate;
    private ObjectMapper objectMapper;
    private SubmissionHandlerSal submissionHandlerSal;

    private final String submissionNamespace = "dev-namespace";
    private final String baseUrl = "http://submission-handler-service.dev-namespace";

    @BeforeEach
    void setUp() {
        accessManagementSal = mock(AccessManagementSal.class);
        restTemplate = mock(RestTemplate.class);
        objectMapper = new ObjectMapper();

        submissionHandlerSal = new SubmissionHandlerSal(accessManagementSal, restTemplate, submissionNamespace, objectMapper);
    }

    @Test
    void testGetSubmissionById_success() {
        String submissionId = "sub-123";
        String clientId = "client-1";
        String token = "Bearer xyz";

        when(accessManagementSal.getToken(clientId)).thenReturn(token);

        Submission expectedSubmission = new Submission();
        expectedSubmission.setSubmissionId(submissionId);

        BasicResponse response = new BasicResponse();
        response.setResult(Map.of("submissionId", submissionId));

        ResponseEntity<BasicResponse> mockResponse = new ResponseEntity<>(response, HttpStatus.OK);
        when(restTemplate.exchange(
                anyString(),
                eq(HttpMethod.GET),
                any(HttpEntity.class),
                eq(BasicResponse.class),
                eq(submissionId)
        )).thenReturn(mockResponse);

        Submission actual = submissionHandlerSal.getSubmissionById(submissionId, clientId);

        assertNotNull(actual);
        assertEquals(submissionId, actual.getSubmissionId());
    }

    @Test
    void testGetSubmissionById_failure_4xx() {
        String submissionId = "sub-err";
        String clientId = "client-1";
        when(accessManagementSal.getToken(clientId)).thenReturn("token");

        when(restTemplate.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(BasicResponse.class), eq(submissionId)))
                .thenThrow(new HttpClientErrorException(HttpStatus.NOT_FOUND, "Not Found"));

        RuntimeException ex = assertThrows(RuntimeException.class, () ->
                submissionHandlerSal.getSubmissionById(submissionId, clientId)
        );

        assertTrue(ex.getMessage().contains("Failed to fetch Submission"));
    }

    @Test
    void testGetInsuredAssetsForSubmission_success() {
        String submissionId = "sub-789";
        String clientId = "client-2";
        String lob = "AVIATION";
        int page = 0;
        int size = 10;

        when(accessManagementSal.getToken(clientId)).thenReturn("token");

        InsuredAssetsResponse mockResponseBody = new InsuredAssetsResponse();
        ResponseEntity<InsuredAssetsResponse> mockResponse = new ResponseEntity<>(mockResponseBody, HttpStatus.OK);

        when(restTemplate.exchange(
                contains("/insured-assets/AVIATION_WAR/" + submissionId),
                eq(HttpMethod.GET),
                any(HttpEntity.class),
                eq(InsuredAssetsResponse.class)
        )).thenReturn(mockResponse);

        InsuredAssetsResponse result = submissionHandlerSal.getInsuredAssetsForSubmission(submissionId, lob, page, size, clientId);

        assertNotNull(result);
        verify(restTemplate).exchange(
                contains("/insured-assets/AVIATION_WAR/" + submissionId),
                eq(HttpMethod.GET),
                any(HttpEntity.class),
                eq(InsuredAssetsResponse.class)
        );
    }

    @Test
    void testGetInsuredAssetsForSubmission_exception() {
        String submissionId = "sub-ex";
        String clientId = "client-x";
        when(accessManagementSal.getToken(clientId)).thenReturn("token");

        when(restTemplate.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(InsuredAssetsResponse.class)))
                .thenThrow(new RuntimeException("Simulated"));

        RuntimeException exception = assertThrows(RuntimeException.class, () ->
                submissionHandlerSal.getInsuredAssetsForSubmission(submissionId, "LOB", 0, 5, clientId));

        assertEquals("Failed to fetch insured assets", exception.getMessage());
    }
}