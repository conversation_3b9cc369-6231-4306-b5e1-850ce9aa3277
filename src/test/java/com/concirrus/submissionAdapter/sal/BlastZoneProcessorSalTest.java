package com.concirrus.submissionAdapter.sal;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import com.concirrus.submissionAdapter.dto.submission.BasicResponse;
import com.concirrus.submissionAdapter.dto.miAnalysis.MiAnalysisJob;
import com.concirrus.submissionAdapter.dto.miAnalysis.MiAnalysisJobRequest;
import com.concirrus.submissionAdapter.utils.SlackNotifier;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@ExtendWith(MockitoExtension.class)
@DisplayName("BlastZoneProcessorSal Tests")
class BlastZoneProcessorSalTest {

    @Mock
    private RestTemplate restTemplate;

    @Mock
    private SlackNotifier slackNotifier;

    @InjectMocks
    private BlastZoneProcessorSal blastZoneProcessorSal;

    private MiAnalysisJobRequest testJobRequest;
    private MiAnalysisJob testMiAnalysisJob;
    private BasicResponse<MiAnalysisJob> testCreateJobResponse;
    private BasicResponse<String> testTriggerJobResponse;

    @BeforeEach
    void setUp() {
        // Setup test job request
        testJobRequest = new MiAnalysisJobRequest();
        testJobRequest.setQuoteId("QUOTE123");
        testJobRequest.setSubmissionId("SUB123");

        // Setup test MI analysis job
        testMiAnalysisJob = new MiAnalysisJob();
        testMiAnalysisJob.setJobId("JOB123");
        testMiAnalysisJob.setQuoteId("QUOTE123");
        testMiAnalysisJob.setSubmissionId("SUB123");

        // Setup create job response
        testCreateJobResponse = new BasicResponse<>();
        testCreateJobResponse.setResult(testMiAnalysisJob);

        // Setup trigger job response
        testTriggerJobResponse = new BasicResponse<>();
        testTriggerJobResponse.setResult("Job triggered successfully");
    }

    @Nested
    @DisplayName("Create MI Analysis Jobs Tests")
    class CreateMiAnalysisJobsTests {

        @Test
        @DisplayName("Should create multiple MI analysis jobs successfully")
        void shouldCreateMultipleMiAnalysisJobsSuccessfully() {
            // Given
            MiAnalysisJobRequest request2 = new MiAnalysisJobRequest();
            request2.setQuoteId("QUOTE456");
            request2.setSubmissionId("SUB456");

            List<MiAnalysisJobRequest> requests = Arrays.asList(testJobRequest, request2);

            ResponseEntity<BasicResponse<MiAnalysisJob>> responseEntity =
                    new ResponseEntity<>(testCreateJobResponse, HttpStatus.OK);

            when(restTemplate.exchange(
                    eq("http://blast-zone-processor/mi-analysis/jobs"),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    any(ParameterizedTypeReference.class)
            )).thenReturn(responseEntity);

            // When
            assertDoesNotThrow(() -> blastZoneProcessorSal.createMiAnalysisJobs(requests));

            // Then
            verify(restTemplate, times(2)).exchange(
                    eq("http://blast-zone-processor/mi-analysis/jobs"),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    any(ParameterizedTypeReference.class)
            );
        }

        @Test
        @DisplayName("Should handle null job requests list")
        void shouldHandleNullJobRequestsList() {
            // When & Then
            assertDoesNotThrow(() -> blastZoneProcessorSal.createMiAnalysisJobs(null));
            verifyNoInteractions(restTemplate);
        }

        @Test
        @DisplayName("Should handle empty job requests list")
        void shouldHandleEmptyJobRequestsList() {
            // When & Then
            assertDoesNotThrow(() -> blastZoneProcessorSal.createMiAnalysisJobs(Collections.emptyList()));
            verifyNoInteractions(restTemplate);
        }

        @Test
        @DisplayName("Should skip null job requests in list")
        void shouldSkipNullJobRequestsInList() {
            // Given
            List<MiAnalysisJobRequest> requests = Arrays.asList(testJobRequest, null, testJobRequest);

            ResponseEntity<BasicResponse<MiAnalysisJob>> responseEntity =
                    new ResponseEntity<>(testCreateJobResponse, HttpStatus.OK);

            when(restTemplate.exchange(
                    eq("http://blast-zone-processor/mi-analysis/jobs"),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    any(ParameterizedTypeReference.class)
            )).thenReturn(responseEntity);

            // When
            assertDoesNotThrow(() -> blastZoneProcessorSal.createMiAnalysisJobs(requests));

            // Then
            verify(restTemplate, times(2)).exchange(
                    eq("http://blast-zone-processor/mi-analysis/jobs"),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    any(ParameterizedTypeReference.class)
            );
        }

        @Test
        @DisplayName("Should handle HTTP client error during job creation")
        void shouldHandleHttpClientErrorDuringJobCreation() {
            // Given
            List<MiAnalysisJobRequest> requests = Collections.singletonList(testJobRequest);

            HttpClientErrorException clientException = new HttpClientErrorException(
                    HttpStatus.BAD_REQUEST, "Bad Request", "Invalid request".getBytes(), null);

            when(restTemplate.exchange(
                    eq("http://blast-zone-processor/mi-analysis/jobs"),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    any(ParameterizedTypeReference.class)
            )).thenThrow(clientException);

            // When
            assertDoesNotThrow(() -> blastZoneProcessorSal.createMiAnalysisJobs(requests));

            // Then
            verify(slackNotifier).sendAlert(contains("Failed to create MI analysis job"));
        }

        @Test
        @DisplayName("Should handle HTTP server error during job creation")
        void shouldHandleHttpServerErrorDuringJobCreation() {
            // Given
            List<MiAnalysisJobRequest> requests = Collections.singletonList(testJobRequest);

            HttpServerErrorException serverException = new HttpServerErrorException(
                    HttpStatus.INTERNAL_SERVER_ERROR, "Server Error");

            when(restTemplate.exchange(
                    eq("http://blast-zone-processor/mi-analysis/jobs"),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    any(ParameterizedTypeReference.class)
            )).thenThrow(serverException);

            // When
            assertDoesNotThrow(() -> blastZoneProcessorSal.createMiAnalysisJobs(requests));

            // Then
            verify(slackNotifier).sendAlert(contains("Failed to create MI analysis job"));
        }

        @Test
        @DisplayName("Should handle generic exception during job creation")
        void shouldHandleGenericExceptionDuringJobCreation() {
            // Given
            List<MiAnalysisJobRequest> requests = Collections.singletonList(testJobRequest);

            RestClientException restException = new RestClientException("Connection timeout");

            when(restTemplate.exchange(
                    eq("http://blast-zone-processor/mi-analysis/jobs"),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    any(ParameterizedTypeReference.class)
            )).thenThrow(restException);

            // When
            assertDoesNotThrow(() -> blastZoneProcessorSal.createMiAnalysisJobs(requests));

            // Then
            verify(slackNotifier).sendAlert(contains("Failed to create MI analysis job"));
        }

        @Test
        @DisplayName("Should handle null response body during job creation")
        void shouldHandleNullResponseBodyDuringJobCreation() {
            // Given
            List<MiAnalysisJobRequest> requests = Collections.singletonList(testJobRequest);

            ResponseEntity<BasicResponse<MiAnalysisJob>> responseEntity =
                    new ResponseEntity<>(null, HttpStatus.OK);

            when(restTemplate.exchange(
                    eq("http://blast-zone-processor/mi-analysis/jobs"),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    any(ParameterizedTypeReference.class)
            )).thenReturn(responseEntity);

            // When
            assertDoesNotThrow(() -> blastZoneProcessorSal.createMiAnalysisJobs(requests));

            // Then
            verifyNoInteractions(slackNotifier);
        }

        @Test
        @DisplayName("Should handle null result in response during job creation")
        void shouldHandleNullResultInResponseDuringJobCreation() {
            // Given
            List<MiAnalysisJobRequest> requests = Collections.singletonList(testJobRequest);

            BasicResponse<MiAnalysisJob> responseWithNullResult = new BasicResponse<>();
            responseWithNullResult.setResult(null);

            ResponseEntity<BasicResponse<MiAnalysisJob>> responseEntity =
                    new ResponseEntity<>(responseWithNullResult, HttpStatus.OK);

            when(restTemplate.exchange(
                    eq("http://blast-zone-processor/mi-analysis/jobs"),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    any(ParameterizedTypeReference.class)
            )).thenReturn(responseEntity);

            // When
            assertDoesNotThrow(() -> blastZoneProcessorSal.createMiAnalysisJobs(requests));

            // Then
            verifyNoInteractions(slackNotifier);
        }

        @Test
        @DisplayName("Should set correct headers for job creation request")
        void shouldSetCorrectHeadersForJobCreationRequest() {
            // Given
            List<MiAnalysisJobRequest> requests = Collections.singletonList(testJobRequest);

            ResponseEntity<BasicResponse<MiAnalysisJob>> responseEntity =
                    new ResponseEntity<>(testCreateJobResponse, HttpStatus.OK);

            when(restTemplate.exchange(
                    eq("http://blast-zone-processor/mi-analysis/jobs"),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    any(ParameterizedTypeReference.class)
            )).thenReturn(responseEntity);

            // When
            blastZoneProcessorSal.createMiAnalysisJobs(requests);

            // Then
            ArgumentCaptor<HttpEntity> entityCaptor = ArgumentCaptor.forClass(HttpEntity.class);
            verify(restTemplate).exchange(
                    eq("http://blast-zone-processor/mi-analysis/jobs"),
                    eq(HttpMethod.POST),
                    entityCaptor.capture(),
                    any(ParameterizedTypeReference.class)
            );

            HttpHeaders capturedHeaders = entityCaptor.getValue().getHeaders();
            assertEquals(MediaType.APPLICATION_JSON, capturedHeaders.getContentType());
            assertTrue(capturedHeaders.getAccept().contains(MediaType.APPLICATION_JSON));
        }
    }

    @Nested
    @DisplayName("Trigger MI Analysis Jobs Tests")
    class TriggerMiAnalysisJobsTests {

        @Test
        @DisplayName("Should trigger multiple MI analysis jobs successfully")
        void shouldTriggerMultipleMiAnalysisJobsSuccessfully() {
            // Given
            List<String> jobIds = Arrays.asList("JOB123", "JOB456");

            ResponseEntity<BasicResponse<String>> responseEntity =
                    new ResponseEntity<>(testTriggerJobResponse, HttpStatus.OK);

            when(restTemplate.exchange(
                    anyString(),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    any(ParameterizedTypeReference.class)
            )).thenReturn(responseEntity);

            // When
            assertDoesNotThrow(() -> blastZoneProcessorSal.triggerMiAnalysisJobs(jobIds));

            // Then
            verify(restTemplate, times(2)).exchange(
                    anyString(),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    any(ParameterizedTypeReference.class)
            );
        }

        @Test
        @DisplayName("Should handle null job IDs list")
        void shouldHandleNullJobIdsList() {
            // When & Then
            assertDoesNotThrow(() -> blastZoneProcessorSal.triggerMiAnalysisJobs(null));
            verifyNoInteractions(restTemplate);
        }

        @Test
        @DisplayName("Should handle empty job IDs list")
        void shouldHandleEmptyJobIdsList() {
            // When & Then
            assertDoesNotThrow(() -> blastZoneProcessorSal.triggerMiAnalysisJobs(Collections.emptyList()));
            verifyNoInteractions(restTemplate);
        }

        @Test
        @DisplayName("Should skip null and empty job IDs")
        void shouldSkipNullAndEmptyJobIds() {
            // Given
            List<String> jobIds = Arrays.asList("JOB123", null, "", "  ", "JOB456");

            ResponseEntity<BasicResponse<String>> responseEntity =
                    new ResponseEntity<>(testTriggerJobResponse, HttpStatus.OK);

            when(restTemplate.exchange(
                    anyString(),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    any(ParameterizedTypeReference.class)
            )).thenReturn(responseEntity);

            // When
            assertDoesNotThrow(() -> blastZoneProcessorSal.triggerMiAnalysisJobs(jobIds));

            // Then
            verify(restTemplate, times(2)).exchange(
                    anyString(),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    any(ParameterizedTypeReference.class)
            );
        }

        @Test
        @DisplayName("Should handle HTTP client error during job triggering")
        void shouldHandleHttpClientErrorDuringJobTriggering() {
            // Given
            List<String> jobIds = Collections.singletonList("JOB123");

            HttpClientErrorException clientException = new HttpClientErrorException(
                    HttpStatus.NOT_FOUND, "Not Found");

            when(restTemplate.exchange(
                    anyString(),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    any(ParameterizedTypeReference.class)
            )).thenThrow(clientException);

            // When
            assertDoesNotThrow(() -> blastZoneProcessorSal.triggerMiAnalysisJobs(jobIds));

            // Then
            verify(slackNotifier).sendAlert(contains("Failed to trigger MI analysis"));
        }

        @Test
        @DisplayName("Should handle HTTP server error during job triggering")
        void shouldHandleHttpServerErrorDuringJobTriggering() {
            // Given
            List<String> jobIds = Collections.singletonList("JOB123");

            HttpServerErrorException serverException = new HttpServerErrorException(
                    HttpStatus.INTERNAL_SERVER_ERROR, "Server Error");

            when(restTemplate.exchange(
                    anyString(),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    any(ParameterizedTypeReference.class)
            )).thenThrow(serverException);

            // When
            assertDoesNotThrow(() -> blastZoneProcessorSal.triggerMiAnalysisJobs(jobIds));

            // Then
            verify(slackNotifier).sendAlert(contains("Failed to trigger MI analysis"));
        }

        @Test
        @DisplayName("Should handle generic exception during job triggering")
        void shouldHandleGenericExceptionDuringJobTriggering() {
            // Given
            List<String> jobIds = Collections.singletonList("JOB123");

            RestClientException restException = new RestClientException("Connection timeout");

            when(restTemplate.exchange(
                    anyString(),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    any(ParameterizedTypeReference.class)
            )).thenThrow(restException);

            // When
            assertDoesNotThrow(() -> blastZoneProcessorSal.triggerMiAnalysisJobs(jobIds));

            // Then
            verify(slackNotifier).sendAlert(contains("Failed to trigger MI analysis"));
        }

        @Test
        @DisplayName("Should construct correct URL for job triggering")
        void shouldConstructCorrectUrlForJobTriggering() {
            // Given
            List<String> jobIds = Collections.singletonList("JOB123");

            ResponseEntity<BasicResponse<String>> responseEntity =
                    new ResponseEntity<>(testTriggerJobResponse, HttpStatus.OK);

            when(restTemplate.exchange(
                    anyString(),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    any(ParameterizedTypeReference.class)
            )).thenReturn(responseEntity);

            // When
            blastZoneProcessorSal.triggerMiAnalysisJobs(jobIds);

            // Then
            verify(restTemplate).exchange(
                    eq("http://blast-zone-processor/mi-analysis/job/JOB123"),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    any(ParameterizedTypeReference.class)
            );
        }

        @Test
        @DisplayName("Should handle non-2xx response status during job triggering")
        void shouldHandleNon2xxResponseStatusDuringJobTriggering() {
            // Given
            List<String> jobIds = Collections.singletonList("JOB123");

            ResponseEntity<BasicResponse<String>> responseEntity =
                    new ResponseEntity<>(testTriggerJobResponse, HttpStatus.BAD_REQUEST);

            when(restTemplate.exchange(
                    anyString(),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    any(ParameterizedTypeReference.class)
            )).thenReturn(responseEntity);

            // When
            assertDoesNotThrow(() -> blastZoneProcessorSal.triggerMiAnalysisJobs(jobIds));

            // Then
            verifyNoInteractions(slackNotifier);
        }

        @Test
        @DisplayName("Should handle null response body during job triggering")
        void shouldHandleNullResponseBodyDuringJobTriggering() {
            // Given
            List<String> jobIds = Collections.singletonList("JOB123");

            ResponseEntity<BasicResponse<String>> responseEntity =
                    new ResponseEntity<>(null, HttpStatus.OK);

            when(restTemplate.exchange(
                    anyString(),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    any(ParameterizedTypeReference.class)
            )).thenReturn(responseEntity);

            // When
            assertDoesNotThrow(() -> blastZoneProcessorSal.triggerMiAnalysisJobs(jobIds));

            // Then
            verifyNoInteractions(slackNotifier);
        }
    }

    @Nested
    @DisplayName("Clear Submission Data Tests")
    class ClearSubmissionDataTests {

        @Test
        @DisplayName("Should return true on successful deletion")
        void shouldReturnTrueOnSuccessfulDeletion() {
            // Given
            String submissionId = "SUB123";
            String url = "http://blast-zone-processor/api/blast-zone/delete-mapping?submissionId=" + submissionId;

            ResponseEntity<String> responseEntity = new ResponseEntity<>("Deleted", HttpStatus.OK);

            when(restTemplate.exchange(
                    eq(url),
                    eq(HttpMethod.DELETE),
                    any(HttpEntity.class),
                    eq(String.class)
            )).thenReturn(responseEntity);

            // When
            boolean result = blastZoneProcessorSal.clearSubmissionData(submissionId);

            // Then
            assertTrue(result);
        }

        @Test
        @DisplayName("Should return false on non-2xx response")
        void shouldReturnFalseOnNon2xxResponse() {
            // Given
            String submissionId = "SUB123";
            String url = "http://blast-zone-processor/api/blast-zone/delete-mapping?submissionId=" + submissionId;

            ResponseEntity<String> responseEntity = new ResponseEntity<>("Error", HttpStatus.BAD_REQUEST);

            when(restTemplate.exchange(
                    eq(url),
                    eq(HttpMethod.DELETE),
                    any(HttpEntity.class),
                    eq(String.class)
            )).thenReturn(responseEntity);

            // When
            boolean result = blastZoneProcessorSal.clearSubmissionData(submissionId);

            // Then
            assertFalse(result);
        }

        @Test
        @DisplayName("Should return false on exception")
        void shouldReturnFalseOnException() {
            // Given
            String submissionId = "SUB123";
            String url = "http://blast-zone-processor/api/blast-zone/delete-mapping?submissionId=" + submissionId;

            when(restTemplate.exchange(
                    eq(url),
                    eq(HttpMethod.DELETE),
                    any(HttpEntity.class),
                    eq(String.class)
            )).thenThrow(new RestClientException("Timeout"));

            // When
            boolean result = blastZoneProcessorSal.clearSubmissionData(submissionId);

            // Then
            assertFalse(result);
        }


    }

    @Nested
    @DisplayName("Recompute Blast Zone Attributes Tests")
    class RecomputeBlastZoneAttributesTests {

        @Test
        @DisplayName("Should successfully trigger blast zone recomputation")
        void shouldSuccessfullyTriggerBlastZoneRecomputation() {
            // Given
            String expectedUrl = "http://blast-zone-processor/api/blast-zone/recompute";
            ResponseEntity<String> successResponse = new ResponseEntity<>("Recomputation triggered", HttpStatus.OK);

            when(restTemplate.exchange(
                    eq(expectedUrl),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    eq(String.class)
            )).thenReturn(successResponse);

            // When
            assertDoesNotThrow(() -> blastZoneProcessorSal.recomputeBlastZoneAttributes());

            // Then
            verify(restTemplate).exchange(
                    eq(expectedUrl),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    eq(String.class)
            );
            verifyNoInteractions(slackNotifier);
        }

        @Test
        @DisplayName("Should handle non-2xx response status and not throw exception")
        void shouldHandleNon2xxResponseStatusAndNotThrowException() {
            // Given
            String expectedUrl = "http://blast-zone-processor/api/blast-zone/recompute";
            ResponseEntity<String> errorResponse = new ResponseEntity<>("Bad Request", HttpStatus.BAD_REQUEST);

            when(restTemplate.exchange(
                    eq(expectedUrl),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    eq(String.class)
            )).thenReturn(errorResponse);

            // When
            assertDoesNotThrow(() -> blastZoneProcessorSal.recomputeBlastZoneAttributes());

            // Then
            verify(restTemplate).exchange(
                    eq(expectedUrl),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    eq(String.class)
            );
            verifyNoInteractions(slackNotifier);
        }

        @Test
        @DisplayName("Should throw RuntimeException when service call fails")
        void shouldThrowRuntimeExceptionWhenServiceCallFails() {
            // Given
            String expectedUrl = "http://blast-zone-processor/api/blast-zone/recompute";
            RestClientException restException = new RestClientException("Connection timeout");

            when(restTemplate.exchange(
                    eq(expectedUrl),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    eq(String.class)
            )).thenThrow(restException);

            // When & Then
            RuntimeException exception = assertThrows(RuntimeException.class, () ->
                    blastZoneProcessorSal.recomputeBlastZoneAttributes());

            assertEquals("Failed to recompute blast zones", exception.getMessage());
            assertEquals(restException, exception.getCause());

            verify(restTemplate).exchange(
                    eq(expectedUrl),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    eq(String.class)
            );
            verifyNoInteractions(slackNotifier);
        }
    }
}
