package com.concirrus.submissionAdapter.sal;

import com.concirrus.submissionAdapter.dto.submission.BasicResponse;
import com.concirrus.submissionAdapter.dto.submission.ClientConfigResponse;
import com.concirrus.submissionAdapter.dto.submission.TokenResponse;
import com.concirrus.submissionAdapter.sal.AccessManagementSal.AccessManagementException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@DisplayName("AccessManagementSal Tests")
class AccessManagementSalTest {

    private RestTemplate restTemplate;
    private AccessManagementSal accessManagementSal;

    private final String namespace = "dev-namespace";
    private final String baseUrl = "http://access-management-service." + namespace;
    private final String clientId = "CLIENT123";

    @BeforeEach
    void setUp() {
        restTemplate = mock(RestTemplate.class);
        accessManagementSal = new AccessManagementSal(restTemplate, namespace);
    }

    @Nested
    @DisplayName("Get Client Config Tests")
    class GetClientConfigTests {

        @Test
        @DisplayName("Should successfully get client config")
        void shouldSuccessfullyGetClientConfig() {
            // Given
            ClientConfigResponse clientConfigResponse = new ClientConfigResponse();
            clientConfigResponse.setId("CONFIG123");
            clientConfigResponse.setClientId(clientId);
            clientConfigResponse.setTenantName("test-tenant");
            clientConfigResponse.setClientName("Test Client");
            clientConfigResponse.setAuthUrl("https://auth.example.com");
            clientConfigResponse.setApiUrl("https://api.example.com");

            BasicResponse<ClientConfigResponse> basicResponse = new BasicResponse<>();
            basicResponse.setResult(clientConfigResponse);

            ResponseEntity<BasicResponse<ClientConfigResponse>> responseEntity =
                    new ResponseEntity<>(basicResponse, HttpStatus.OK);

            String expectedUrl = baseUrl + "/access-management/api/v1/access-management/client//client/" + clientId;

            when(restTemplate.exchange(
                    eq(expectedUrl),
                    eq(HttpMethod.GET),
                    eq(null),
                    any(ParameterizedTypeReference.class)
            )).thenReturn(responseEntity);

            // When
            ClientConfigResponse result = accessManagementSal.getClientConfigByClientId(clientId);

            // Then
            assertNotNull(result);
            assertEquals("CONFIG123", result.getId());
            assertEquals(clientId, result.getClientId());
            assertEquals("test-tenant", result.getTenantName());
            assertEquals("Test Client", result.getClientName());
            assertEquals("https://auth.example.com", result.getAuthUrl());
            assertEquals("https://api.example.com", result.getApiUrl());
        }

        @Test
        @DisplayName("Should throw IllegalArgumentException for null client ID")
        void shouldThrowIllegalArgumentExceptionForNullClientId() {
            // When & Then
            IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                    accessManagementSal.getClientConfigByClientId(null));

            assertEquals("Client ID cannot be null or empty", exception.getMessage());
        }

        @Test
        @DisplayName("Should throw IllegalArgumentException for empty client ID")
        void shouldThrowIllegalArgumentExceptionForEmptyClientId() {
            // When & Then
            IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                    accessManagementSal.getClientConfigByClientId("   "));

            assertEquals("Client ID cannot be null or empty", exception.getMessage());
        }

        @Test
        @DisplayName("Should handle HTTP client error")
        void shouldHandleHttpClientError() {
            // Given
            String expectedUrl = baseUrl + "/access-management/api/v1/access-management/client//client/" + clientId;

            when(restTemplate.exchange(
                    eq(expectedUrl),
                    eq(HttpMethod.GET),
                    eq(null),
                    any(ParameterizedTypeReference.class)
            )).thenThrow(new HttpClientErrorException(HttpStatus.NOT_FOUND, "Client not found"));

            // When & Then
            AccessManagementException exception = assertThrows(AccessManagementException.class, () ->
                    accessManagementSal.getClientConfigByClientId(clientId));

            assertTrue(exception.getMessage().contains("Failed to fetch client config due to HTTP error"));
            assertTrue(exception.getCause() instanceof HttpClientErrorException);
        }

        @Test
        @DisplayName("Should handle HTTP server error")
        void shouldHandleHttpServerError() {
            // Given
            String expectedUrl = baseUrl + "/access-management/api/v1/access-management/client//client/" + clientId;

            when(restTemplate.exchange(
                    eq(expectedUrl),
                    eq(HttpMethod.GET),
                    eq(null),
                    any(ParameterizedTypeReference.class)
            )).thenThrow(new HttpServerErrorException(HttpStatus.INTERNAL_SERVER_ERROR, "Server error"));

            // When & Then
            AccessManagementException exception = assertThrows(AccessManagementException.class, () ->
                    accessManagementSal.getClientConfigByClientId(clientId));

            assertTrue(exception.getMessage().contains("Failed to fetch client config due to HTTP error"));
            assertTrue(exception.getCause() instanceof HttpServerErrorException);
        }

        @Test
        @DisplayName("Should handle network error")
        void shouldHandleNetworkError() {
            // Given
            String expectedUrl = baseUrl + "/access-management/api/v1/access-management/client//client/" + clientId;

            when(restTemplate.exchange(
                    eq(expectedUrl),
                    eq(HttpMethod.GET),
                    eq(null),
                    any(ParameterizedTypeReference.class)
            )).thenThrow(new RestClientException("Connection timeout"));

            // When & Then
            AccessManagementException exception = assertThrows(AccessManagementException.class, () ->
                    accessManagementSal.getClientConfigByClientId(clientId));

            assertEquals("Network error while calling access management service", exception.getMessage());
            assertTrue(exception.getCause() instanceof RestClientException);
        }

        @Test
        @DisplayName("Should handle null response body")
        void shouldHandleNullResponseBody() {
            // Given
            ResponseEntity<BasicResponse<ClientConfigResponse>> responseEntity =
                    new ResponseEntity<>(null, HttpStatus.OK);

            String expectedUrl = baseUrl + "/access-management/api/v1/access-management/client//client/" + clientId;

            when(restTemplate.exchange(
                    eq(expectedUrl),
                    eq(HttpMethod.GET),
                    eq(null),
                    any(ParameterizedTypeReference.class)
            )).thenReturn(responseEntity);

            // When & Then
            AccessManagementException exception = assertThrows(AccessManagementException.class, () ->
                    accessManagementSal.getClientConfigByClientId(clientId));

            assertEquals("Received null response body from access management service", exception.getMessage());
        }

        @Test
        @DisplayName("Should handle null client config response")
        void shouldHandleNullClientConfigResponse() {
            // Given
            BasicResponse<ClientConfigResponse> basicResponse = new BasicResponse<>();
            basicResponse.setResult(null);

            ResponseEntity<BasicResponse<ClientConfigResponse>> responseEntity =
                    new ResponseEntity<>(basicResponse, HttpStatus.OK);

            String expectedUrl = baseUrl + "/access-management/api/v1/access-management/client//client/" + clientId;

            when(restTemplate.exchange(
                    eq(expectedUrl),
                    eq(HttpMethod.GET),
                    eq(null),
                    any(ParameterizedTypeReference.class)
            )).thenReturn(responseEntity);

            // When & Then
            AccessManagementException exception = assertThrows(AccessManagementException.class, () ->
                    accessManagementSal.getClientConfigByClientId(clientId));

            assertEquals("Token response is null", exception.getMessage());
        }

        @Test
        @DisplayName("Should handle non-OK status code")
        void shouldHandleNonOkStatusCode() {
            // Given
            ClientConfigResponse clientConfigResponse = new ClientConfigResponse();
            BasicResponse<ClientConfigResponse> basicResponse = new BasicResponse<>();
            basicResponse.setResult(clientConfigResponse);

            ResponseEntity<BasicResponse<ClientConfigResponse>> responseEntity =
                    new ResponseEntity<>(basicResponse, HttpStatus.ACCEPTED);

            String expectedUrl = baseUrl + "/access-management/api/v1/access-management/client//client/" + clientId;

            when(restTemplate.exchange(
                    eq(expectedUrl),
                    eq(HttpMethod.GET),
                    eq(null),
                    any(ParameterizedTypeReference.class)
            )).thenReturn(responseEntity);

            // When & Then
            AccessManagementException exception = assertThrows(AccessManagementException.class, () ->
                    accessManagementSal.getClientConfigByClientId(clientId));

            assertTrue(exception.getMessage().contains("Unexpected response status"));
        }
    }

    @Nested
    @DisplayName("Get Token Tests")
    class GetTokenTests {

        @Test
        @DisplayName("Should successfully get token")
        void testGetToken_success() {
        TokenResponse tokenResponse = new TokenResponse();
        tokenResponse.setAccessToken("test-access-token");

        BasicResponse<TokenResponse> basicResponse = new BasicResponse<>();
        basicResponse.setResult(tokenResponse);

        ResponseEntity<BasicResponse<TokenResponse>> responseEntity =
                new ResponseEntity<>(basicResponse, HttpStatus.OK);

        when(restTemplate.exchange(
                eq(baseUrl + "/access-management/api/v1/access-management/token"),
                eq(HttpMethod.GET),
                any(HttpEntity.class),
                any(ParameterizedTypeReference.class)
        )).thenReturn(responseEntity);

        String token = accessManagementSal.getToken(clientId);

        assertEquals("test-access-token", token);
    }

        @Test
        @DisplayName("Should throw exception for null client ID")
        void testGetToken_invalidClientId_null() {
        Exception ex = assertThrows(IllegalArgumentException.class, () ->
                accessManagementSal.getToken(null)
        );
        assertEquals("Client ID cannot be null or empty", ex.getMessage());
    }

        @Test
        @DisplayName("Should throw exception for empty client ID")
        void testGetToken_invalidClientId_empty() {
        Exception ex = assertThrows(IllegalArgumentException.class, () ->
                accessManagementSal.getToken("  ")
        );
        assertEquals("Client ID cannot be null or empty", ex.getMessage());
    }

        @Test
        @DisplayName("Should handle HTTP client error")
        void testGetToken_httpClientError() {
        when(restTemplate.exchange(
                anyString(),
                eq(HttpMethod.GET),
                any(HttpEntity.class),
                any(ParameterizedTypeReference.class)
        )).thenThrow(new HttpClientErrorException(HttpStatus.UNAUTHORIZED, "Unauthorized"));

        AccessManagementException ex = assertThrows(AccessManagementException.class, () ->
                accessManagementSal.getToken(clientId)
        );

        assertTrue(ex.getMessage().contains("Failed to fetch token due to HTTP error"));
    }

        @Test
        @DisplayName("Should handle network error")
        void testGetToken_networkError() {
        when(restTemplate.exchange(
                anyString(),
                eq(HttpMethod.GET),
                any(HttpEntity.class),
                any(ParameterizedTypeReference.class)
        )).thenThrow(new RuntimeException("Connection refused"));

        RuntimeException ex = assertThrows(RuntimeException.class, () ->
                accessManagementSal.getToken(clientId)
        );

        assertTrue(ex.getMessage().contains("Connection refused"));
    }

        @Test
        @DisplayName("Should handle null response body")
        void testGetToken_nullResponseBody() {
        ResponseEntity<BasicResponse<TokenResponse>> responseEntity = new ResponseEntity<>(null, HttpStatus.OK);

        when(restTemplate.exchange(
                anyString(),
                eq(HttpMethod.GET),
                any(HttpEntity.class),
                any(ParameterizedTypeReference.class)
        )).thenReturn(responseEntity);

        AccessManagementException ex = assertThrows(AccessManagementException.class, () ->
                accessManagementSal.getToken(clientId)
        );

        assertEquals("Received null response body from access management service", ex.getMessage());
    }

        @Test
        @DisplayName("Should handle null token response")
        void testGetToken_nullTokenResponse() {
        BasicResponse<TokenResponse> response = new BasicResponse<>();
        response.setResult(null);

        ResponseEntity<BasicResponse<TokenResponse>> responseEntity = new ResponseEntity<>(response, HttpStatus.OK);

        when(restTemplate.exchange(
                anyString(),
                eq(HttpMethod.GET),
                any(HttpEntity.class),
                any(ParameterizedTypeReference.class)
        )).thenReturn(responseEntity);

        AccessManagementException ex = assertThrows(AccessManagementException.class, () ->
                accessManagementSal.getToken(clientId)
        );

        assertEquals("Token response is null", ex.getMessage());
    }

        @Test
        @DisplayName("Should handle empty token")
        void testGetToken_emptyToken() {
        TokenResponse tokenResponse = new TokenResponse();
        tokenResponse.setAccessToken("   "); // blank token

        BasicResponse<TokenResponse> basicResponse = new BasicResponse<>();
        basicResponse.setResult(tokenResponse);

        ResponseEntity<BasicResponse<TokenResponse>> responseEntity = new ResponseEntity<>(basicResponse, HttpStatus.OK);

        when(restTemplate.exchange(
                anyString(),
                eq(HttpMethod.GET),
                any(HttpEntity.class),
                any(ParameterizedTypeReference.class)
        )).thenReturn(responseEntity);

        AccessManagementException ex = assertThrows(AccessManagementException.class, () ->
                accessManagementSal.getToken(clientId)
        );

            assertEquals("Access token is null or empty", ex.getMessage());
        }
    }
}