package com.concirrus.submissionAdapter.sal;

import com.concirrus.submissionAdapter.dto.submission.Quote;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@DisplayName("QuoteServiceSal Tests")
class QuoteServiceSalTest {

    private RestTemplate restTemplate;
    private AccessManagementSal accessManagementSal;
    private ObjectMapper objectMapper;
    private QuoteServiceSal quoteServiceSal;

    private final String baseUrl = "http://quote-service";

    @BeforeEach
    void setUp() {
        restTemplate = mock(RestTemplate.class);
        accessManagementSal = mock(AccessManagementSal.class);
        objectMapper = new ObjectMapper();
        quoteServiceSal = new QuoteServiceSal(restTemplate, accessManagementSal, baseUrl, objectMapper);
    }

    @Test
    void testGetQuoteById_success() {
        String quoteId = "quote-123";
        String clientId = "client-abc";
        String token = "Bearer test-token";

        when(accessManagementSal.getToken(clientId)).thenReturn(token);

        Map<String, Object> mockQuoteResponse = new HashMap<>();
        mockQuoteResponse.put("quoteId", quoteId);
        Map<String, Object> riskDetails = new HashMap<>();
        riskDetails.put("inceptionDate", "2023-01-01");
        riskDetails.put("expiryDate", "2023-12-31");
        riskDetails.put("policyCurrency", "USD");
        riskDetails.put("limits", 10000.0);
        riskDetails.put("excess", 500.0);
        mockQuoteResponse.put("riskDetails", riskDetails);

        ResponseEntity<Object> mockResponse = new ResponseEntity<>(mockQuoteResponse, HttpStatus.OK);
        when(restTemplate.exchange(
                eq(baseUrl + "/quote/" + quoteId),
                eq(HttpMethod.GET),
                any(HttpEntity.class),
                eq(Object.class)
        )).thenReturn(mockResponse);

        Quote quote = quoteServiceSal.getQuoteById(quoteId, clientId);

        assertNotNull(quote);
        assertEquals(quoteId, quote.getQuoteId());
        assertEquals(10000.0, Quote.getLimit(quote));
    }

    @Test
    void testGetQuoteById_nullResponse() {
        String quoteId = "quote-456";
        String clientId = "client-xyz";

        when(accessManagementSal.getToken(clientId)).thenReturn("Bearer token");

        ResponseEntity<Object> mockResponse = new ResponseEntity<>(null, HttpStatus.OK);
        when(restTemplate.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(Object.class)))
                .thenReturn(mockResponse);

        Quote result = quoteServiceSal.getQuoteById(quoteId, clientId);

        assertNull(result);
    }

    @Test
    void testGetQuoteById_exception() {
        String quoteId = "quote-error";
        String clientId = "client-error";

        when(accessManagementSal.getToken(clientId)).thenReturn("token");

        when(restTemplate.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(Object.class)))
                .thenThrow(new RuntimeException("Simulated exception"));

        Quote result = quoteServiceSal.getQuoteById(quoteId, clientId);

        assertNull(result);
    }

    @Test
    void testGetAllQuotesBySubmissionId_success() {
        String submissionId = "submission-1";
        String clientId = "client-123";
        String token = "Bearer test-token";

        when(accessManagementSal.getToken(clientId)).thenReturn(token);

        Map<String, Object> quote1 = new HashMap<>();
        quote1.put("quoteId", "q1");
        quote1.put("price", 1000);

        Map<String, Object> quote2 = new HashMap<>();
        quote2.put("quoteId", "q2");
        quote2.put("price", 2000);

        Map<String, Object> responseBody = new HashMap<>();
        responseBody.put("data", List.of(quote1, quote2));

        ResponseEntity<Object> responseEntity = new ResponseEntity<>(responseBody, HttpStatus.OK);
        when(restTemplate.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(Object.class)))
                .thenReturn(responseEntity);

        List<Quote> quotes = quoteServiceSal.getAllQuotesBySubmissionId(submissionId, clientId);

        assertEquals(2, quotes.size());
        assertEquals("q1", quotes.get(0).getQuoteId());
        assertEquals("q2", quotes.get(1).getQuoteId());
    }

    @Test
    void testGetAllQuotesBySubmissionId_nullData() {
        String submissionId = "sub-xyz";
        String clientId = "client-xyz";

        when(accessManagementSal.getToken(clientId)).thenReturn("token");

        ResponseEntity<Object> responseEntity = new ResponseEntity<>(null, HttpStatus.OK);
        when(restTemplate.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(Object.class)))
                .thenReturn(responseEntity);

        List<Quote> result = quoteServiceSal.getAllQuotesBySubmissionId(submissionId, clientId);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetAllQuotesBySubmissionId_exception() {
        String submissionId = "sub-error";
        String clientId = "client-error";

        when(accessManagementSal.getToken(clientId)).thenReturn("token");

        when(restTemplate.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(Object.class)))
                .thenThrow(new RuntimeException("Some error"));

        List<Quote> result = quoteServiceSal.getAllQuotesBySubmissionId(submissionId, clientId);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}