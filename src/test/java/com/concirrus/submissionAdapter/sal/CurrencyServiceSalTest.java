package com.concirrus.submissionAdapter.sal;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import com.concirrus.submissionAdapter.dto.currencyExchange.CurrencyRateApiResponse;
import com.concirrus.submissionAdapter.dto.currencyExchange.CurrencyRateRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.*;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@ExtendWith(MockitoExtension.class)
@DisplayName("CurrencyServiceSal Tests")
class CurrencyServiceSalTest {

    @Mock
    private RestTemplate restTemplate;

    @InjectMocks
    private CurrencyServiceSal currencyServiceSal;

    private String testClientId;
    private String testNamespace;
    private String expectedUrl;
    private String tenantId;
    private List<CurrencyRateRequest> testRequests;
    private CurrencyRateApiResponse testResponse;

    @BeforeEach
    void setUp() {
        testClientId = "CLIENT123";
        testNamespace = "dev-namespace";
        expectedUrl = "http://currency-management-service.dev-namespace/platform/currency-management/rates";
        tenantId = "tenant-id";
        
        // Setup test currency rate requests
        CurrencyRateRequest request1 = new CurrencyRateRequest();
        request1.setFromCcy("USD");
        request1.setToCcy("EUR");
        request1.setAsOfDate(String.valueOf(LocalDate.now()));
        
        CurrencyRateRequest request2 = new CurrencyRateRequest();
        request2.setFromCcy("GBP");
        request2.setToCcy("USD");
        request2.setAsOfDate(String.valueOf(LocalDate.now()));
        
        testRequests = Arrays.asList(request1, request2);
        
        // Setup test response
        testResponse = new CurrencyRateApiResponse();
        testResponse.setSuccess(true);
        
        // Use reflection to set the currencyServiceNamespace field
        try {
            var field = CurrencyServiceSal.class.getDeclaredField("currencyServiceNamespace");
            field.setAccessible(true);
            field.set(currencyServiceSal, testNamespace);
        } catch (Exception e) {
            fail("Failed to set currencyServiceNamespace field: " + e.getMessage());
        }
    }

    @Nested
    @DisplayName("Get Currency Rates Tests")
    class GetCurrencyRatesTests {

        @Test
        @DisplayName("Should successfully get currency rates for valid requests")
        void shouldSuccessfullyGetCurrencyRatesForValidRequests() {
            // Given
            ResponseEntity<CurrencyRateApiResponse> responseEntity = 
                    new ResponseEntity<>(testResponse, HttpStatus.OK);
            
            when(restTemplate.exchange(
                    eq(expectedUrl),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    eq(CurrencyRateApiResponse.class)
            )).thenReturn(responseEntity);

            // When
            CurrencyRateApiResponse result = currencyServiceSal.getCurrencyRates(testRequests, testClientId);

            // Then
            assertNotNull(result);
            assertEquals(testResponse, result);
            assertTrue(result.getSuccess());
            
            // Verify the request was made with correct parameters
            verify(restTemplate).exchange(
                    eq(expectedUrl),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    eq(CurrencyRateApiResponse.class)
            );
        }

        @Test
        @DisplayName("Should set correct headers in the request")
        void shouldSetCorrectHeadersInTheRequest() {
            // Given
            ResponseEntity<CurrencyRateApiResponse> responseEntity = 
                    new ResponseEntity<>(testResponse, HttpStatus.OK);
            
            when(restTemplate.exchange(
                    eq(expectedUrl),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    eq(CurrencyRateApiResponse.class)
            )).thenReturn(responseEntity);

            // When
            currencyServiceSal.getCurrencyRates(testRequests, testClientId);

            // Then
            ArgumentCaptor<HttpEntity> entityCaptor = ArgumentCaptor.forClass(HttpEntity.class);
            verify(restTemplate).exchange(
                    eq(expectedUrl),
                    eq(HttpMethod.POST),
                    entityCaptor.capture(),
                    eq(CurrencyRateApiResponse.class)
            );
            
            HttpHeaders capturedHeaders = entityCaptor.getValue().getHeaders();
            assertEquals(MediaType.APPLICATION_JSON, capturedHeaders.getContentType());
            assertTrue(capturedHeaders.getAccept().contains(MediaType.APPLICATION_JSON));
            assertEquals(testClientId, capturedHeaders.getFirst("referenceId"));
            assertNotNull(capturedHeaders.getFirst("requestId"));
        }

        @Test
        @DisplayName("Should include request body with currency rate requests")
        void shouldIncludeRequestBodyWithCurrencyRateRequests() {
            // Given
            ResponseEntity<CurrencyRateApiResponse> responseEntity = 
                    new ResponseEntity<>(testResponse, HttpStatus.OK);
            
            when(restTemplate.exchange(
                    eq(expectedUrl),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    eq(CurrencyRateApiResponse.class)
            )).thenReturn(responseEntity);

            // When
            currencyServiceSal.getCurrencyRates(testRequests, testClientId);

            // Then
            ArgumentCaptor<HttpEntity> entityCaptor = ArgumentCaptor.forClass(HttpEntity.class);
            verify(restTemplate).exchange(
                    eq(expectedUrl),
                    eq(HttpMethod.POST),
                    entityCaptor.capture(),
                    eq(CurrencyRateApiResponse.class)
            );
            
            @SuppressWarnings("unchecked")
            List<CurrencyRateRequest> capturedBody = (List<CurrencyRateRequest>) entityCaptor.getValue().getBody();
            assertEquals(testRequests, capturedBody);
        }

        @Test
        @DisplayName("Should handle empty requests list")
        void shouldHandleEmptyRequestsList() {
            // Given
            List<CurrencyRateRequest> emptyRequests = Collections.emptyList();
            ResponseEntity<CurrencyRateApiResponse> responseEntity = 
                    new ResponseEntity<>(testResponse, HttpStatus.OK);
            
            when(restTemplate.exchange(
                    eq(expectedUrl),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    eq(CurrencyRateApiResponse.class)
            )).thenReturn(responseEntity);

            // When
            CurrencyRateApiResponse result = currencyServiceSal.getCurrencyRates(emptyRequests, testClientId);

            // Then
            assertNotNull(result);
            assertEquals(testResponse, result);
        }

        @Test
        @DisplayName("Should handle null requests list")
        void shouldHandleNullRequestsList() {
            // Given
            ResponseEntity<CurrencyRateApiResponse> responseEntity = 
                    new ResponseEntity<>(testResponse, HttpStatus.OK);
            
            when(restTemplate.exchange(
                    eq(expectedUrl),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    eq(CurrencyRateApiResponse.class)
            )).thenReturn(responseEntity);

            // When
            CurrencyRateApiResponse result = currencyServiceSal.getCurrencyRates(null, testClientId);

            // Then
            assertNotNull(result);
            assertEquals(testResponse, result);
        }

        @Test
        @DisplayName("Should handle null client ID")
        void shouldHandleNullClientId() {
            // Given
            ResponseEntity<CurrencyRateApiResponse> responseEntity = 
                    new ResponseEntity<>(testResponse, HttpStatus.OK);
            
            when(restTemplate.exchange(
                    eq(expectedUrl),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    eq(CurrencyRateApiResponse.class)
            )).thenReturn(responseEntity);

            // When
            CurrencyRateApiResponse result = currencyServiceSal.getCurrencyRates(testRequests, null);

            // Then
            assertNotNull(result);
            assertEquals(testResponse, result);
            
            // Verify null client ID was set in header
            ArgumentCaptor<HttpEntity> entityCaptor = ArgumentCaptor.forClass(HttpEntity.class);
            verify(restTemplate).exchange(
                    eq(expectedUrl),
                    eq(HttpMethod.POST),
                    entityCaptor.capture(),
                    eq(CurrencyRateApiResponse.class)
            );
            
            HttpHeaders capturedHeaders = entityCaptor.getValue().getHeaders();
            assertNull(capturedHeaders.getFirst("X-Tenant-Id"));
        }

        @Test
        @DisplayName("Should construct correct URL with namespace")
        void shouldConstructCorrectUrlWithNamespace() {
            // Given
            ResponseEntity<CurrencyRateApiResponse> responseEntity = 
                    new ResponseEntity<>(testResponse, HttpStatus.OK);
            
            when(restTemplate.exchange(
                    anyString(),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    eq(CurrencyRateApiResponse.class)
            )).thenReturn(responseEntity);

            // When
            currencyServiceSal.getCurrencyRates(testRequests, testClientId);

            // Then
            verify(restTemplate).exchange(
                    eq(expectedUrl),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    eq(CurrencyRateApiResponse.class)
            );
        }

        @Test
        @DisplayName("Should propagate HttpClientErrorException")
        void shouldPropagateHttpClientErrorException() {
            // Given
            HttpClientErrorException clientException = new HttpClientErrorException(
                    HttpStatus.BAD_REQUEST, "Bad Request", "Invalid currency".getBytes(), null);
            
            when(restTemplate.exchange(
                    eq(expectedUrl),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    eq(CurrencyRateApiResponse.class)
            )).thenThrow(clientException);

            // When & Then
            assertThrows(HttpClientErrorException.class, () ->
                    currencyServiceSal.getCurrencyRates(testRequests, testClientId));
        }

        @Test
        @DisplayName("Should propagate HttpServerErrorException")
        void shouldPropagateHttpServerErrorException() {
            // Given
            HttpServerErrorException serverException = new HttpServerErrorException(
                    HttpStatus.INTERNAL_SERVER_ERROR, "Server Error");
            
            when(restTemplate.exchange(
                    eq(expectedUrl),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    eq(CurrencyRateApiResponse.class)
            )).thenThrow(serverException);

            // When & Then
            assertThrows(HttpServerErrorException.class, () ->
                    currencyServiceSal.getCurrencyRates(testRequests, testClientId));
        }

        @Test
        @DisplayName("Should propagate RestClientException")
        void shouldPropagateRestClientException() {
            // Given
            RestClientException restException = new RestClientException("Connection timeout");
            
            when(restTemplate.exchange(
                    eq(expectedUrl),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    eq(CurrencyRateApiResponse.class)
            )).thenThrow(restException);

            // When & Then
            assertThrows(RestClientException.class, () ->
                    currencyServiceSal.getCurrencyRates(testRequests, testClientId));
        }

        @Test
        @DisplayName("Should return response body even if null")
        void shouldReturnResponseBodyEvenIfNull() {
            // Given
            ResponseEntity<CurrencyRateApiResponse> responseEntity = 
                    new ResponseEntity<>(null, HttpStatus.OK);
            
            when(restTemplate.exchange(
                    eq(expectedUrl),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    eq(CurrencyRateApiResponse.class)
            )).thenReturn(responseEntity);

            // When
            CurrencyRateApiResponse result = currencyServiceSal.getCurrencyRates(testRequests, testClientId);

            // Then
            assertNull(result);
        }

        @Test
        @DisplayName("Should generate unique request ID for each call")
        void shouldGenerateUniqueRequestIdForEachCall() {
            // Given
            ResponseEntity<CurrencyRateApiResponse> responseEntity = 
                    new ResponseEntity<>(testResponse, HttpStatus.OK);
            
            when(restTemplate.exchange(
                    eq(expectedUrl),
                    eq(HttpMethod.POST),
                    any(HttpEntity.class),
                    eq(CurrencyRateApiResponse.class)
            )).thenReturn(responseEntity);

            // When
            currencyServiceSal.getCurrencyRates(testRequests, testClientId);
            currencyServiceSal.getCurrencyRates(testRequests, testClientId);

            // Then
            ArgumentCaptor<HttpEntity> entityCaptor = ArgumentCaptor.forClass(HttpEntity.class);
            verify(restTemplate, times(2)).exchange(
                    eq(expectedUrl),
                    eq(HttpMethod.POST),
                    entityCaptor.capture(),
                    eq(CurrencyRateApiResponse.class)
            );
            
            List<HttpEntity> capturedEntities = entityCaptor.getAllValues();
            String requestId1 = capturedEntities.get(0).getHeaders().getFirst("requestId");
            String requestId2 = capturedEntities.get(1).getHeaders().getFirst("requestId");
            
            assertNotNull(requestId1);
            assertNotNull(requestId2);
            assertNotEquals(requestId1, requestId2);
        }
    }
}
