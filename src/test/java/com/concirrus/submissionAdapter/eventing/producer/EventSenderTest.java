package com.concirrus.submissionAdapter.eventing.producer;

import com.concirrus.submissionAdapter.dto.enums.State;
import com.concirrus.submissionAdapter.dto.enums.UpdateType;
import com.concirrus.submissionAdapter.dto.workflow.LocationEventDTO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.cloud.spring.pubsub.core.PubSubTemplate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("EventSender Tests")
class EventSenderTest {

    private PubSubTemplate pubSubTemplate;

    private ObjectMapper objectMapper;

    private EventSender eventSender;

    private LocationEventDTO testLocationEvent;
    private String testTopic;
    private String testJsonPayload;

    @BeforeEach
    void setUp() {
        testTopic = "blastzone-location-processor-dev";
        testJsonPayload = "{\"submissionId\":\"SUB123\",\"locationId\":\"LOC123\",\"longitude\":-74.006,\"latitude\":40.7128,\"state\":\"IN_REVIEW\",\"updateType\":\"LOCATION_CREATED\",\"jobId\":\"JOB123\"}";
         pubSubTemplate = mock(PubSubTemplate.class);
         objectMapper = mock(ObjectMapper.class);
        eventSender = new EventSender(pubSubTemplate, objectMapper);

        // Set the topic property using reflection
        ReflectionTestUtils.setField(eventSender, "blastzoneLocationTopic", testTopic);

        // Setup test location event
        testLocationEvent = LocationEventDTO.builder()
                .submissionId("SUB123")
                .locationId("LOC123")
                .longitude(-74.006)
                .latitude(40.7128)
                .state(State.IN_REVIEW)
                .updateType(UpdateType.LOCATION_CREATED)
                .jobId("JOB123")
                .build();
    }

    @Nested
    @DisplayName("Send Blastzone Location Event Tests")
    class SendBlastzoneLocationEventTests {

        @Test
        @DisplayName("Should successfully send single location event")
        void shouldSuccessfullySendSingleLocationEvent() throws JsonProcessingException {
            // Given
            when(objectMapper.writeValueAsString(testLocationEvent)).thenReturn(testJsonPayload);

            // When
            eventSender.sendBlastzoneLocationEvent(testLocationEvent);

            // Then
            verify(objectMapper).writeValueAsString(testLocationEvent);
            verify(pubSubTemplate).publish(testTopic, testJsonPayload);
        }

        @Test
        @DisplayName("Should handle JSON serialization exception")
        void shouldHandleJsonSerializationException() throws JsonProcessingException {
            // Given
            JsonProcessingException jsonException = new JsonProcessingException("Serialization failed") {};
            when(objectMapper.writeValueAsString(testLocationEvent)).thenThrow(jsonException);

            // When & Then
            RuntimeException exception = assertThrows(RuntimeException.class, () ->
                    eventSender.sendBlastzoneLocationEvent(testLocationEvent));
            
            assertEquals("Error while sending message to Pub/Sub", exception.getMessage());
            assertEquals(jsonException, exception.getCause());
            verify(pubSubTemplate, never()).publish(anyString(), anyString());
        }

        @Test
        @DisplayName("Should handle PubSub publishing exception")
        void shouldHandlePubSubPublishingException() throws JsonProcessingException {
            // Given
            when(objectMapper.writeValueAsString(testLocationEvent)).thenReturn(testJsonPayload);
            RuntimeException pubSubException = new RuntimeException("PubSub connection failed");
            doThrow(pubSubException).when(pubSubTemplate).publish(testTopic, testJsonPayload);

            // When & Then
            RuntimeException exception = assertThrows(RuntimeException.class, () ->
                    eventSender.sendBlastzoneLocationEvent(testLocationEvent));
            
            assertEquals("Error while sending message to Pub/Sub", exception.getMessage());
            assertEquals(pubSubException, exception.getCause());
        }

        @Test
        @DisplayName("Should handle null location event")
        void shouldHandleNullLocationEvent() throws JsonProcessingException {
            // Given
            when(objectMapper.writeValueAsString(null)).thenReturn("null");

            // When
            eventSender.sendBlastzoneLocationEvent(null);

            // Then
            verify(objectMapper).writeValueAsString(null);
            verify(pubSubTemplate).publish(testTopic, "null");
        }

        @Test
        @DisplayName("Should use correct topic configuration")
        void shouldUseCorrectTopicConfiguration() throws JsonProcessingException {
            // Given
            when(objectMapper.writeValueAsString(testLocationEvent)).thenReturn(testJsonPayload);

            // When
            eventSender.sendBlastzoneLocationEvent(testLocationEvent);

            // Then
            ArgumentCaptor<String> topicCaptor = ArgumentCaptor.forClass(String.class);
            ArgumentCaptor<String> payloadCaptor = ArgumentCaptor.forClass(String.class);
            verify(pubSubTemplate).publish(topicCaptor.capture(), payloadCaptor.capture());
            
            assertEquals(testTopic, topicCaptor.getValue());
            assertEquals(testJsonPayload, payloadCaptor.getValue());
        }
    }

    @Nested
    @DisplayName("Send Location Events Tests")
    class SendLocationEventsTests {

        @Test
        @DisplayName("Should successfully send multiple location events")
        void shouldSuccessfullySendMultipleLocationEvents() throws JsonProcessingException {
            // Given
            LocationEventDTO event1 = LocationEventDTO.builder()
                    .submissionId("SUB123")
                    .locationId("LOC123")
                    .longitude(-74.006)
                    .latitude(40.7128)
                    .state(State.IN_REVIEW)
                    .updateType(UpdateType.LOCATION_CREATED)
                    .jobId("JOB123")
                    .build();

            LocationEventDTO event2 = LocationEventDTO.builder()
                    .submissionId("SUB456")
                    .locationId("LOC456")
                    .longitude(-118.2437)
                    .latitude(34.0522)
                    .state(State.QUOTED)
                    .updateType(UpdateType.LOCATION_UPDATED)
                    .jobId("JOB456")
                    .build();

            List<LocationEventDTO> events = Arrays.asList(event1, event2);
            
            when(objectMapper.writeValueAsString(event1)).thenReturn("event1_json");
            when(objectMapper.writeValueAsString(event2)).thenReturn("event2_json");

            // When
            eventSender.sendLocationEvents(events);

            // Then
            verify(objectMapper).writeValueAsString(event1);
            verify(objectMapper).writeValueAsString(event2);
            verify(pubSubTemplate).publish(testTopic, "event1_json");
            verify(pubSubTemplate).publish(testTopic, "event2_json");
        }

        @Test
        @DisplayName("Should handle empty event list")
        void shouldHandleEmptyEventList() {
            // Given
            List<LocationEventDTO> emptyEvents = Collections.emptyList();

            // When
            eventSender.sendLocationEvents(emptyEvents);

            // Then
            verifyNoInteractions(objectMapper);
            verifyNoInteractions(pubSubTemplate);
        }

        @Test
        @DisplayName("Should handle null event list")
        void shouldHandleNullEventList() {
            // When & Then
            assertThrows(NullPointerException.class, () ->
                    eventSender.sendLocationEvents(null));
        }

        @Test
        @DisplayName("Should handle single event in list")
        void shouldHandleSingleEventInList() throws JsonProcessingException {
            // Given
            List<LocationEventDTO> singleEvent = Arrays.asList(testLocationEvent);
            when(objectMapper.writeValueAsString(testLocationEvent)).thenReturn(testJsonPayload);

            // When
            eventSender.sendLocationEvents(singleEvent);

            // Then
            verify(objectMapper).writeValueAsString(testLocationEvent);
            verify(pubSubTemplate).publish(testTopic, testJsonPayload);
        }

        @Test
        @DisplayName("Should continue processing after individual event failure")
        void shouldContinueProcessingAfterIndividualEventFailure() throws JsonProcessingException {
            // Given
            LocationEventDTO event1 = testLocationEvent;
            LocationEventDTO event2 = LocationEventDTO.builder()
                    .submissionId("SUB456")
                    .locationId("LOC456")
                    .longitude(-118.2437)
                    .latitude(34.0522)
                    .state(State.QUOTED)
                    .updateType(UpdateType.LOCATION_UPDATED)
                    .jobId("JOB456")
                    .build();

            List<LocationEventDTO> events = Arrays.asList(event1, event2);
            
            when(objectMapper.writeValueAsString(event1))
                    .thenThrow(new JsonProcessingException("Serialization failed") {});


            // When & Then
            assertThrows(RuntimeException.class, () ->
                    eventSender.sendLocationEvents(events));
            
            // Verify first event processing was attempted
            verify(objectMapper).writeValueAsString(event1);
            // Second event should not be processed due to exception in first
            verify(objectMapper, never()).writeValueAsString(event2);
        }

        @Test
        @DisplayName("Should handle large number of events")
        void shouldHandleLargeNumberOfEvents() throws JsonProcessingException {
            // Given
            List<LocationEventDTO> largeEventList = Arrays.asList(
                    createTestEvent("SUB001", "LOC001", "JOB001"),
                    createTestEvent("SUB002", "LOC002", "JOB002"),
                    createTestEvent("SUB003", "LOC003", "JOB003"),
                    createTestEvent("SUB004", "LOC004", "JOB004"),
                    createTestEvent("SUB005", "LOC005", "JOB005")
            );

            when(objectMapper.writeValueAsString(any(LocationEventDTO.class)))
                    .thenReturn("test_json");

            // When
            eventSender.sendLocationEvents(largeEventList);

            // Then
            verify(objectMapper, times(5)).writeValueAsString(any(LocationEventDTO.class));
            verify(pubSubTemplate, times(5)).publish(testTopic, "test_json");
        }

        private LocationEventDTO createTestEvent(String submissionId, String locationId, String jobId) {
            return LocationEventDTO.builder()
                    .submissionId(submissionId)
                    .locationId(locationId)
                    .longitude(-74.006)
                    .latitude(40.7128)
                    .state(State.IN_REVIEW)
                    .updateType(UpdateType.LOCATION_CREATED)
                    .jobId(jobId)
                    .build();
        }
    }

    @Nested
    @DisplayName("Send Message Method Tests")
    class SendMessageMethodTests {

        @Test
        @DisplayName("Should handle different topic configurations")
        void shouldHandleDifferentTopicConfigurations() throws JsonProcessingException {
            // Given
            String customTopic = "custom-blastzone-topic";
            ReflectionTestUtils.setField(eventSender, "blastzoneLocationTopic", customTopic);
            when(objectMapper.writeValueAsString(testLocationEvent)).thenReturn(testJsonPayload);

            // When
            eventSender.sendBlastzoneLocationEvent(testLocationEvent);

            // Then
            verify(pubSubTemplate).publish(customTopic, testJsonPayload);
        }

        @Test
        @DisplayName("Should handle null topic configuration")
        void shouldHandleNullTopicConfiguration() throws JsonProcessingException {
            // Given
            ReflectionTestUtils.setField(eventSender, "blastzoneLocationTopic", null);
            when(objectMapper.writeValueAsString(testLocationEvent)).thenReturn(testJsonPayload);

            // When
            eventSender.sendBlastzoneLocationEvent(testLocationEvent);

            // Then
            verify(pubSubTemplate).publish(null, testJsonPayload);
        }

        @Test
        @DisplayName("Should handle empty topic configuration")
        void shouldHandleEmptyTopicConfiguration() throws JsonProcessingException {
            // Given
            ReflectionTestUtils.setField(eventSender, "blastzoneLocationTopic", "");
            when(objectMapper.writeValueAsString(testLocationEvent)).thenReturn(testJsonPayload);

            // When
            eventSender.sendBlastzoneLocationEvent(testLocationEvent);

            // Then
            verify(pubSubTemplate).publish("", testJsonPayload);
        }

        @Test
        @DisplayName("Should handle complex location event data")
        void shouldHandleComplexLocationEventData() throws JsonProcessingException {
            // Given
            LocationEventDTO complexEvent = LocationEventDTO.builder()
                    .submissionId("COMPLEX_SUB_123")
                    .locationId("COMPLEX_LOC_456")
                    .longitude(-122.4194)
                    .latitude(37.7749)
                    .state(State.UW_REVIEW)
                    .updateType(UpdateType.LOCATION_COORDINATES_UPDATED)
                    .jobId("COMPLEX_JOB_789")
                    .build();

            String complexJson = "{\"submissionId\":\"COMPLEX_SUB_123\",\"locationId\":\"COMPLEX_LOC_456\",\"longitude\":-122.4194,\"latitude\":37.7749,\"state\":\"UW_REVIEW\",\"updateType\":\"LOCATION_COORDINATES_UPDATED\",\"jobId\":\"COMPLEX_JOB_789\"}";
            when(objectMapper.writeValueAsString(complexEvent)).thenReturn(complexJson);

            // When
            eventSender.sendBlastzoneLocationEvent(complexEvent);

            // Then
            verify(objectMapper).writeValueAsString(complexEvent);
            verify(pubSubTemplate).publish(testTopic, complexJson);
        }

        @Test
        @DisplayName("Should handle event with boundary coordinate values")
        void shouldHandleEventWithBoundaryCoordinateValues() throws JsonProcessingException {
            // Given
            LocationEventDTO boundaryEvent = LocationEventDTO.builder()
                    .submissionId("BOUNDARY_SUB")
                    .locationId("BOUNDARY_LOC")
                    .longitude(180.0) // Maximum longitude
                    .latitude(90.0)   // Maximum latitude
                    .state(State.BOUND)
                    .updateType(UpdateType.LOCATION_DATA_UPDATED)
                    .jobId("BOUNDARY_JOB")
                    .build();

            String boundaryJson = "{\"longitude\":180.0,\"latitude\":90.0}";
            when(objectMapper.writeValueAsString(boundaryEvent)).thenReturn(boundaryJson);

            // When
            eventSender.sendBlastzoneLocationEvent(boundaryEvent);

            // Then
            verify(objectMapper).writeValueAsString(boundaryEvent);
            verify(pubSubTemplate).publish(testTopic, boundaryJson);
        }

        @Test
        @DisplayName("Should handle event with minimum coordinate values")
        void shouldHandleEventWithMinimumCoordinateValues() throws JsonProcessingException {
            // Given
            LocationEventDTO minEvent = LocationEventDTO.builder()
                    .submissionId("MIN_SUB")
                    .locationId("MIN_LOC")
                    .longitude(-180.0) // Minimum longitude
                    .latitude(-90.0)   // Minimum latitude
                    .state(State.DELETED)
                    .updateType(UpdateType.LOCATION_DELETED)
                    .jobId("MIN_JOB")
                    .build();

            String minJson = "{\"longitude\":-180.0,\"latitude\":-90.0}";
            when(objectMapper.writeValueAsString(minEvent)).thenReturn(minJson);

            // When
            eventSender.sendBlastzoneLocationEvent(minEvent);

            // Then
            verify(objectMapper).writeValueAsString(minEvent);
            verify(pubSubTemplate).publish(testTopic, minJson);
        }

        @Test
        @DisplayName("Should handle generic runtime exception during publishing")
        void shouldHandleGenericRuntimeExceptionDuringPublishing() throws JsonProcessingException {
            // Given
            when(objectMapper.writeValueAsString(testLocationEvent)).thenReturn(testJsonPayload);
            RuntimeException genericException = new RuntimeException("Generic error occurred");
            doThrow(genericException).when(pubSubTemplate).publish(testTopic, testJsonPayload);

            // When & Then
            RuntimeException exception = assertThrows(RuntimeException.class, () ->
                    eventSender.sendBlastzoneLocationEvent(testLocationEvent));

            assertEquals("Error while sending message to Pub/Sub", exception.getMessage());
            assertEquals(genericException, exception.getCause());
        }

        @Test
        @DisplayName("Should handle interrupted exception during publishing")
        void shouldHandleInterruptedExceptionDuringPublishing() throws JsonProcessingException {
            // Given
            when(objectMapper.writeValueAsString(testLocationEvent)).thenReturn(testJsonPayload);
            InterruptedException interruptedException = new InterruptedException("Thread interrupted");
            doThrow(new RuntimeException(interruptedException)).when(pubSubTemplate).publish(testTopic, testJsonPayload);

            // When & Then
            RuntimeException exception = assertThrows(RuntimeException.class, () ->
                    eventSender.sendBlastzoneLocationEvent(testLocationEvent));

            assertEquals("Error while sending message to Pub/Sub", exception.getMessage());
            assertNotNull(exception.getCause());
        }
    }

    @Nested
    @DisplayName("Integration and Edge Case Tests")
    class IntegrationAndEdgeCaseTests {

        @Test
        @DisplayName("Should handle all update types correctly")
        void shouldHandleAllUpdateTypesCorrectly() throws JsonProcessingException {
            // Given
            UpdateType[] allUpdateTypes = UpdateType.values();

            for (UpdateType updateType : allUpdateTypes) {
                LocationEventDTO event = LocationEventDTO.builder()
                        .submissionId("SUB_" + updateType.name())
                        .locationId("LOC_" + updateType.name())
                        .longitude(-74.006)
                        .latitude(40.7128)
                        .state(State.IN_REVIEW)
                        .updateType(updateType)
                        .jobId("JOB_" + updateType.name())
                        .build();

                when(objectMapper.writeValueAsString(event)).thenReturn("json_" + updateType.name());

                // When
                eventSender.sendBlastzoneLocationEvent(event);

                // Then
                verify(pubSubTemplate).publish(testTopic, "json_" + updateType.name());
            }
        }

        @Test
        @DisplayName("Should handle all state types correctly")
        void shouldHandleAllStateTypesCorrectly() throws JsonProcessingException {
            // Given
            State[] allStates = State.values();

            for (State state : allStates) {
                LocationEventDTO event = LocationEventDTO.builder()
                        .submissionId("SUB_" + state.name())
                        .locationId("LOC_" + state.name())
                        .longitude(-74.006)
                        .latitude(40.7128)
                        .state(state)
                        .updateType(UpdateType.LOCATION_CREATED)
                        .jobId("JOB_" + state.name())
                        .build();

                when(objectMapper.writeValueAsString(event)).thenReturn("json_" + state.name());

                // When
                eventSender.sendBlastzoneLocationEvent(event);

                // Then
                verify(pubSubTemplate).publish(testTopic, "json_" + state.name());
            }
        }

        @Test
        @DisplayName("Should maintain thread safety for concurrent event sending")
        void shouldMaintainThreadSafetyForConcurrentEventSending() throws JsonProcessingException {
            // Given
            when(objectMapper.writeValueAsString(any(LocationEventDTO.class))).thenReturn(testJsonPayload);

            List<LocationEventDTO> concurrentEvents = Arrays.asList(
                    createTestEvent("CONCURRENT_SUB_1", "CONCURRENT_LOC_1", "CONCURRENT_JOB_1"),
                    createTestEvent("CONCURRENT_SUB_2", "CONCURRENT_LOC_2", "CONCURRENT_JOB_2"),
                    createTestEvent("CONCURRENT_SUB_3", "CONCURRENT_LOC_3", "CONCURRENT_JOB_3")
            );

            // When
            eventSender.sendLocationEvents(concurrentEvents);

            // Then
            verify(objectMapper, times(3)).writeValueAsString(any(LocationEventDTO.class));
            verify(pubSubTemplate, times(3)).publish(testTopic, testJsonPayload);
        }

        private LocationEventDTO createTestEvent(String submissionId, String locationId, String jobId) {
            return LocationEventDTO.builder()
                    .submissionId(submissionId)
                    .locationId(locationId)
                    .longitude(-74.006)
                    .latitude(40.7128)
                    .state(State.IN_REVIEW)
                    .updateType(UpdateType.LOCATION_CREATED)
                    .jobId(jobId)
                    .build();
        }
    }
}
