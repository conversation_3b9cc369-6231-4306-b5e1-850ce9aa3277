package com.concirrus.submissionAdapter.eventing.consumer;

import com.concirrus.submissionAdapter.dto.workflow.TaskCallBack;
import com.concirrus.submissionAdapter.service.TenantService;
import com.concirrus.submissionAdapter.service.politicalViolence.AggregationWorkflowService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("GcpQueueHandler Tests")
class BlastZoneCallbackQueueHandlerTest {

    private ObjectMapper objectMapper;
    @Mock
    private AggregationWorkflowService aggregationWorkflowService;
    private BlastZoneCallbackQueueHandler handler;
    private TenantService tenantService;

    @BeforeEach
    void setup() {
        objectMapper = mock(ObjectMapper.class);
        aggregationWorkflowService = mock(AggregationWorkflowService.class);
        tenantService = mock(TenantService.class);
        handler = new BlastZoneCallbackQueueHandler(objectMapper, aggregationWorkflowService, tenantService);
    }

    @Test
    void testProcessBlastZoneCallbackEvent_validMessage_shouldProcess() throws Exception {
        String message = "{\"taskId\":\"task123\"}";
        TaskCallBack callback = new TaskCallBack();
        callback.setTaskId("task123");
        callback.setClientId("client1");

        when(objectMapper.readValue(message, TaskCallBack.class)).thenReturn(callback);
        when(tenantService.getTenantAlias("client1")).thenReturn("tenant1");

        handler.processBlastZoneCallbackEvent(message);

        verify(aggregationWorkflowService).handleTaskCallback(callback);
    }

    @Test
    void testProcessBlastZoneCallbackEvent_invalidJson_shouldLogError() throws Exception {
        String message = "invalid-json";

        when(objectMapper.readValue(eq(message), eq(TaskCallBack.class)))
                .thenThrow(new JsonProcessingException("Invalid JSON") {});

        handler.processBlastZoneCallbackEvent(message);

        verify(aggregationWorkflowService, never()).handleTaskCallback(any());
    }

    @Test
    void testProcessBlastZoneCallbackEvent_runtimeException_shouldLogError() throws Exception {
        String message = "{\"taskId\":\"task123\"}";
        TaskCallBack callback = new TaskCallBack();
        callback.setTaskId("task123");
        callback.setClientId("client1");
        when(tenantService.getTenantAlias("client1")).thenReturn("tenant1");

        when(objectMapper.readValue(message, TaskCallBack.class)).thenReturn(callback);
        doThrow(new RuntimeException("Some error")).when(aggregationWorkflowService).handleTaskCallback(callback);

        handler.processBlastZoneCallbackEvent(message);

        // Exception is logged, but test will pass unless it propagates
        verify(aggregationWorkflowService).handleTaskCallback(callback);
    }
}