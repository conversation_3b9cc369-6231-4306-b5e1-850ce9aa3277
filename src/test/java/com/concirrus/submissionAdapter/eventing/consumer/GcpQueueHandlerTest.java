package com.concirrus.submissionAdapter.eventing.consumer;

import com.concirrus.submissionAdapter.dto.workflow.SubmissionUpdateEvent;
import com.concirrus.submissionAdapter.dto.enums.ProductType;
import com.concirrus.submissionAdapter.service.FactoryMap;
import com.concirrus.submissionAdapter.service.MessageHandlerFactory;
import com.concirrus.submissionAdapter.service.TenantService;
import com.concirrus.submissionAdapter.utils.SlackNotifier;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("GcpQueueHandler Tests")
class GcpQueueHandlerTest {

    private ObjectMapper objectMapper;
    private FactoryMap factoryMap;
    private SlackNotifier slackNotifier;
    private GcpQueueHandler gcpQueueHandler;
    private TenantService tenantService;
    private List<String> validTenantIds;

    @BeforeEach
    void setUp() {
        objectMapper = mock(ObjectMapper.class);
        factoryMap = mock(FactoryMap.class);
        slackNotifier = mock(SlackNotifier.class);
        tenantService = mock(TenantService.class);
        gcpQueueHandler = new GcpQueueHandler(objectMapper, factoryMap, slackNotifier, tenantService);
    }

    @Test
    void testProcessSubmissionEvent_validEvent_processedSuccessfully() throws Exception {
        String message = "{\"clientId\":\"CLIENT1\",\"submissionId\":\"SUB1\",\"updateType\":\"CREATE\",\"productType\":\"AVIATION_WAR\"}";
        SubmissionUpdateEvent event = new SubmissionUpdateEvent();
        event.setClientId("CLIENT1");
        event.setSubmissionId("SUB1");
        event.setUpdateType("CREATE");
        event.setProductType("AVIATION_WAR");

        when(objectMapper.readValue(message, SubmissionUpdateEvent.class)).thenReturn(event);
        when(tenantService.isValidTenantId("CLIENT1")).thenReturn(true);
        when(tenantService.getTenantAlias("CLIENT1")).thenReturn("tenant1");
        MessageHandlerFactory factory = mock(MessageHandlerFactory.class);
        when(factoryMap.getFactory(ProductType.AVIATION_WAR)).thenReturn(factory);

        gcpQueueHandler.processSubmissionEvent(message);

        verify(factory).processSubmissionUpdate(event);
        verifyNoInteractions(slackNotifier);
    }

    @Test
    void testProcessSubmissionEvent_jsonProcessingException_alertSent() throws Exception {
        String message = "invalid-json";
        when(objectMapper.readValue(eq(message), eq(SubmissionUpdateEvent.class)))
                .thenThrow(new JsonProcessingException("Error parsing") {});

        gcpQueueHandler.processSubmissionEvent(message);

        verify(slackNotifier).sendAlert(contains("Error occurred while parsing"));
    }

    @Test
    void testProcessSubmissionEvent_nullClientId_alertSent() throws Exception {
        String message = "{\"productType\":\"AVIATION_WAR\"}";
        SubmissionUpdateEvent event = new SubmissionUpdateEvent();
        event.setClientId(null);
        event.setProductType("AVIATION_WAR");
        event.setSubmissionId("SUB_ID");

        when(objectMapper.readValue(message, SubmissionUpdateEvent.class)).thenReturn(event);

        gcpQueueHandler.processSubmissionEvent(message);

        verify(slackNotifier).sendAlert(contains("Error occurred while processing"));
    }

    @Test
    void testProcessSubmissionEvent_invalidProductType_alertSent() throws Exception {
        String message = "{\"clientId\":\"client1\",\"productType\":\"UNKNOWN_TYPE\"}";
        SubmissionUpdateEvent event = new SubmissionUpdateEvent();
        event.setClientId("client1");
        event.setProductType("UNKNOWN_TYPE");

        when(tenantService.isValidTenantId("CLIENT1")).thenReturn(true);
        when(tenantService.getTenantAlias("CLIENT1")).thenReturn("tenant1");
        when(objectMapper.readValue(message, SubmissionUpdateEvent.class)).thenReturn(event);

        gcpQueueHandler.processSubmissionEvent(message);

        verify(slackNotifier).sendAlert(contains("Error occurred while processing"));
    }

    @Test
    void testProcessSubmissionEvent_factoryIsNull_logsError() throws Exception {
        String message = "{\"clientId\":\"client1\",\"productType\":\"AVIATION_WAR\"}";
        SubmissionUpdateEvent event = new SubmissionUpdateEvent();
        event.setClientId("client1");
        event.setProductType("AVIATION_WAR");
        when(tenantService.isValidTenantId("client1")).thenReturn(true);
        when(tenantService.getTenantAlias("client1")).thenReturn("tenant1");
        when(objectMapper.readValue(message, SubmissionUpdateEvent.class)).thenReturn(event);
        when(factoryMap.getFactory(ProductType.AVIATION_WAR)).thenReturn(null);

        gcpQueueHandler.processSubmissionEvent(message);

        verifyNoMoreInteractions(slackNotifier);
    }
    @Test
    void testProcessSubmissionEvent_validTenantId_processedSuccessfully() throws Exception {
        String message = "{\"clientId\":\"CLIENT1\",\"submissionId\":\"SUB1\",\"updateType\":\"CREATE\",\"productType\":\"AVIATION_WAR\"}";
        SubmissionUpdateEvent event = new SubmissionUpdateEvent();
        event.setClientId("CLIENT1");
        event.setSubmissionId("SUB1");
        event.setUpdateType("CREATE");
        event.setProductType("AVIATION_WAR");

        when(tenantService.isValidTenantId("CLIENT1")).thenReturn(true);
        when(tenantService.getTenantAlias("CLIENT1")).thenReturn("tenant1");
        when(objectMapper.readValue(message, SubmissionUpdateEvent.class)).thenReturn(event);
        MessageHandlerFactory factory = mock(MessageHandlerFactory.class);
        when(factoryMap.getFactory(ProductType.AVIATION_WAR)).thenReturn(factory);

        gcpQueueHandler.processSubmissionEvent(message);

        verify(factory).processSubmissionUpdate(event);
        verifyNoInteractions(slackNotifier);
    }
    @Test
    void testProcessSubmissionEvent_invalidTenantId_skipped() throws Exception {
        String message = "{\"clientId\":\"CLIENT2\",\"submissionId\":\"SUB1\",\"updateType\":\"CREATE\",\"productType\":\"AVIATION_WAR\"}";
        SubmissionUpdateEvent event = new SubmissionUpdateEvent();
        event.setClientId("CLIENT2");
        event.setSubmissionId("SUB1");
        event.setUpdateType("CREATE");
        event.setProductType("AVIATION_WAR");

        when(objectMapper.readValue(message, SubmissionUpdateEvent.class)).thenReturn(event);

        gcpQueueHandler.processSubmissionEvent(message);

        verifyNoInteractions(factoryMap);
        verifyNoInteractions(slackNotifier);
    }
}