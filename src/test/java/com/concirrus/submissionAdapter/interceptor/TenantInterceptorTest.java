package com.concirrus.submissionAdapter.interceptor;

import com.concirrus.submissionAdapter.config.TenantContextHolder;
import com.concirrus.submissionAdapter.service.TenantService;
import com.concirrus.submissionAdapter.utils.TenantInterceptor;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import static com.concirrus.submissionAdapter.constants.Constants.CLIENT_ID;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("TenantInterceptor Tests")
class TenantInterceptorTest {

    @Mock
    private TenantService tenantService;

    @InjectMocks
    private TenantInterceptor tenantInterceptor;

    private MockHttpServletRequest request;
    private MockHttpServletResponse response;
    private Object handler;
    private String testClientId;
    private String testTenantAlias;

    @BeforeEach
    void setUp() {
        request = new MockHttpServletRequest();
        response = new MockHttpServletResponse();
        handler = new Object();
        testClientId = "CLIENT123";
        testTenantAlias = "test-tenant-alias";
        
        // Clear any existing tenant context
        TenantContextHolder.clearTenantId();
        TenantContextHolder.clearTenantAlias();
    }

    @AfterEach
    void tearDown() {
        // Clean up tenant context after each test
        TenantContextHolder.clearTenantId();
        TenantContextHolder.clearTenantAlias();
    }

    @Nested
    @DisplayName("Pre Handle Tests")
    class PreHandleTests {

        @Test
        @DisplayName("Should set tenant context when valid client ID header is present")
        void shouldSetTenantContextWhenValidClientIdHeaderIsPresent() throws Exception {
            // Given
            request.addHeader(CLIENT_ID, testClientId);
            when(tenantService.getTenantAlias(testClientId)).thenReturn(testTenantAlias);

            // When
            boolean result = tenantInterceptor.preHandle(request, response, handler);

            // Then
            assertTrue(result);
            assertEquals(testClientId, TenantContextHolder.getTenantId());
            assertEquals(testTenantAlias, TenantContextHolder.getTenantAlias());
            verify(tenantService).getTenantAlias(testClientId);
        }

        @Test
        @DisplayName("Should handle missing client ID header gracefully")
        void shouldHandleMissingClientIdHeaderGracefully() throws Exception {
            // Given - No client ID header

            RuntimeException exception = assertThrows(RuntimeException.class, () ->
                    tenantInterceptor.preHandle(request, response, handler));
            assertEquals("Client ID is null in request", exception.getMessage());
            verifyNoInteractions(tenantService);
        }

        @Test
        @DisplayName("Should handle empty client ID header")
        void shouldHandleEmptyClientIdHeader() throws Exception {
            // Given
            request.addHeader(CLIENT_ID, "");
            when(tenantService.getTenantAlias("")).thenReturn(testTenantAlias);

            // When
            boolean result = tenantInterceptor.preHandle(request, response, handler);

            // Then
            assertTrue(result);
            assertEquals("", TenantContextHolder.getTenantId());
            assertEquals(testTenantAlias, TenantContextHolder.getTenantAlias());
            verify(tenantService).getTenantAlias("");
        }

        @Test
        @DisplayName("Should handle null tenant alias from service")
        void shouldHandleNullTenantAliasFromService() throws Exception {
            // Given
            request.addHeader(CLIENT_ID, testClientId);
            when(tenantService.getTenantAlias(testClientId)).thenReturn(null);

           RuntimeException exception = assertThrows(RuntimeException.class, () ->
                    tenantInterceptor.preHandle(request, response, handler));

            // Then
            assertEquals("Tenant alias is null for client ID " + testClientId, exception.getMessage());
            verify(tenantService).getTenantAlias(testClientId);
        }


        @Test
        @DisplayName("Should handle multiple client ID headers")
        void shouldHandleMultipleClientIdHeaders() throws Exception {
            // Given
            request.addHeader(CLIENT_ID, testClientId);
            request.addHeader(CLIENT_ID, "CLIENT456"); // Second header value
            when(tenantService.getTenantAlias(testClientId)).thenReturn(testTenantAlias);

            // When
            boolean result = tenantInterceptor.preHandle(request, response, handler);

            // Then
            assertTrue(result);
            assertEquals(testClientId, TenantContextHolder.getTenantId()); // Should use first value
            assertEquals(testTenantAlias, TenantContextHolder.getTenantAlias());
            verify(tenantService).getTenantAlias(testClientId);
        }

        @Test
        @DisplayName("Should handle different client ID formats")
        void shouldHandleDifferentClientIdFormats() throws Exception {
            // Given
            String[] clientIdFormats = {
                "CLIENT123",
                "client-with-dashes",
                "CLIENT_WITH_UNDERSCORES",
                "123456789",
                "MixedCaseClient123",
                "client.with.dots"
            };

            for (String clientId : clientIdFormats) {
                // Reset for each iteration
                TenantContextHolder.clearTenantId();
                TenantContextHolder.clearTenantAlias();
                request = new MockHttpServletRequest();
                
                request.addHeader(CLIENT_ID, clientId);
                when(tenantService.getTenantAlias(clientId)).thenReturn("alias-" + clientId);

                // When
                boolean result = tenantInterceptor.preHandle(request, response, handler);

                // Then
                assertTrue(result);
                assertEquals(clientId, TenantContextHolder.getTenantId());
                assertEquals("alias-" + clientId, TenantContextHolder.getTenantAlias());
            }

            verify(tenantService, times(clientIdFormats.length)).getTenantAlias(anyString());
        }

        @Test
        @DisplayName("Should overwrite existing tenant context")
        void shouldOverwriteExistingTenantContext() throws Exception {
            // Given
            TenantContextHolder.setTenantContext("EXISTING_CLIENT", "existing-alias");
            
            request.addHeader(CLIENT_ID, testClientId);
            when(tenantService.getTenantAlias(testClientId)).thenReturn(testTenantAlias);

            // When
            boolean result = tenantInterceptor.preHandle(request, response, handler);

            // Then
            assertTrue(result);
            assertEquals(testClientId, TenantContextHolder.getTenantId());
            assertEquals(testTenantAlias, TenantContextHolder.getTenantAlias());
            verify(tenantService).getTenantAlias(testClientId);
        }

        @Test
        @DisplayName("Should handle whitespace in client ID header")
        void shouldHandleWhitespaceInClientIdHeader() throws Exception {
            // Given
            String clientIdWithWhitespace = "  " + testClientId + "  ";
            request.addHeader(CLIENT_ID, clientIdWithWhitespace);
            when(tenantService.getTenantAlias(clientIdWithWhitespace)).thenReturn(testTenantAlias);

            // When
            boolean result = tenantInterceptor.preHandle(request, response, handler);

            // Then
            assertTrue(result);
            assertEquals(clientIdWithWhitespace, TenantContextHolder.getTenantId());
            assertEquals(testTenantAlias, TenantContextHolder.getTenantAlias());
            verify(tenantService).getTenantAlias(clientIdWithWhitespace);
        }
    }

    @Nested
    @DisplayName("After Completion Tests")
    class AfterCompletionTests {

        @Test
        @DisplayName("Should clear tenant context after request completion")
        void shouldClearTenantContextAfterRequestCompletion() throws Exception {
            // Given
            TenantContextHolder.setTenantContext(testClientId, testTenantAlias);

            // When
            tenantInterceptor.afterCompletion(request, response, handler, null);

            // Then
            assertNull(TenantContextHolder.getTenantId());
            assertNull(TenantContextHolder.getTenantAlias());
        }

        @Test
        @DisplayName("Should clear tenant context even when exception occurred")
        void shouldClearTenantContextEvenWhenExceptionOccurred() throws Exception {
            // Given
            TenantContextHolder.setTenantContext(testClientId, testTenantAlias);
            Exception testException = new RuntimeException("Request processing failed");

            // When
            tenantInterceptor.afterCompletion(request, response, handler, testException);

            // Then
            assertNull(TenantContextHolder.getTenantId());
            assertNull(TenantContextHolder.getTenantAlias());
        }

        @Test
        @DisplayName("Should handle null exception parameter")
        void shouldHandleNullExceptionParameter() throws Exception {
            // Given
            TenantContextHolder.setTenantContext(testClientId, testTenantAlias);

            // When
            tenantInterceptor.afterCompletion(request, response, handler, null);

            // Then
            assertNull(TenantContextHolder.getTenantId());
            assertNull(TenantContextHolder.getTenantAlias());
        }

        @Test
        @DisplayName("Should handle cleanup when no tenant context was set")
        void shouldHandleCleanupWhenNoTenantContextWasSet() throws Exception {
            // Given - No tenant context set

            // When
            tenantInterceptor.afterCompletion(request, response, handler, null);

            // Then
            assertNull(TenantContextHolder.getTenantId());
            assertNull(TenantContextHolder.getTenantAlias());
        }

        @Test
        @DisplayName("Should handle multiple cleanup calls")
        void shouldHandleMultipleCleanupCalls() throws Exception {
            // Given
            TenantContextHolder.setTenantContext(testClientId, testTenantAlias);

            // When
            tenantInterceptor.afterCompletion(request, response, handler, null);
            tenantInterceptor.afterCompletion(request, response, handler, null);

            // Then
            assertNull(TenantContextHolder.getTenantId());
            assertNull(TenantContextHolder.getTenantAlias());
        }
    }

    @Nested
    @DisplayName("Integration Tests")
    class IntegrationTests {

        @Test
        @DisplayName("Should handle complete request lifecycle")
        void shouldHandleCompleteRequestLifecycle() throws Exception {
            // Given
            request.addHeader(CLIENT_ID, testClientId);
            when(tenantService.getTenantAlias(testClientId)).thenReturn(testTenantAlias);

            // When - Pre handle
            boolean preHandleResult = tenantInterceptor.preHandle(request, response, handler);

            // Then - Context should be set
            assertTrue(preHandleResult);
            assertEquals(testClientId, TenantContextHolder.getTenantId());
            assertEquals(testTenantAlias, TenantContextHolder.getTenantAlias());

            // When - After completion
            tenantInterceptor.afterCompletion(request, response, handler, null);

            // Then - Context should be cleared
            assertNull(TenantContextHolder.getTenantId());
            assertNull(TenantContextHolder.getTenantAlias());
        }

        @Test
        @DisplayName("Should handle request with service exception and cleanup")
        void shouldHandleRequestWithServiceExceptionAndThrowException() throws Exception {
            // Given
            request.addHeader(CLIENT_ID, testClientId);
            when(tenantService.getTenantAlias(testClientId))
                    .thenThrow(new RuntimeException("Service error"));


            RuntimeException exception = assertThrows(RuntimeException.class, () ->
                    tenantInterceptor.preHandle(request, response, handler));
            assertEquals("Service error", exception.getMessage());

            // When - After completion
            tenantInterceptor.afterCompletion(request, response, handler, exception);

            // Then - Context should be cleared
            assertNull(TenantContextHolder.getTenantId());
            assertNull(TenantContextHolder.getTenantAlias());
        }

        @Test
        @DisplayName("Should handle concurrent requests independently")
        void shouldHandleConcurrentRequestsIndependently() throws Exception {
            // Given
            String client1 = "CLIENT1";
            String client2 = "CLIENT2";
            String alias1 = "alias1";
            String alias2 = "alias2";

            MockHttpServletRequest request1 = new MockHttpServletRequest();
            MockHttpServletRequest request2 = new MockHttpServletRequest();
            
            request1.addHeader(CLIENT_ID, client1);
            request2.addHeader(CLIENT_ID, client2);
            
            when(tenantService.getTenantAlias(client1)).thenReturn(alias1);
            when(tenantService.getTenantAlias(client2)).thenReturn(alias2);

            // When - Process first request
            tenantInterceptor.preHandle(request1, response, handler);
            assertEquals(client1, TenantContextHolder.getTenantId());
            assertEquals(alias1, TenantContextHolder.getTenantAlias());

            // Process second request (simulating different thread context)
            tenantInterceptor.preHandle(request2, response, handler);
            assertEquals(client2, TenantContextHolder.getTenantId());
            assertEquals(alias2, TenantContextHolder.getTenantAlias());

            // Clean up
            tenantInterceptor.afterCompletion(request2, response, handler, null);
            assertNull(TenantContextHolder.getTenantId());
            assertNull(TenantContextHolder.getTenantAlias());
        }
    }
}
