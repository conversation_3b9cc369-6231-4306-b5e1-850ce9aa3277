
server:
  port: 8080
  servlet:
    context-path: /submission-adapter
    session:
      timeout: 180m

management:
  endpoints:
    web:
      base-path: /manage
      exposure:
        include: health, info, prometheus, restart
  endpoint:
    info:
      enabled: true
    health:
      enabled: true
      show-details: always
    restart:
      enabled: true

cloud:
  provider: aws
  aws:
    credentials:
      access-key:
      secret-key:
    region:
      static: us-east-2
  messaging:
    subscription:
      submission-update-queue: ${SUBMISSION_ADAPTER_UPDATE_SUBSCRIPTION:submission-adapter-update-events-dev-sub}
      blastzone-callback-queue: ${WORKFLOW_CALLBACK_SUBSCRIPTION:submission-adapter-callback-dev-sub}
    producer:
      blastzone-location-event: ${BLAST_ZONE_PROCESSOR_TOPIC:blastzone-location-processor-dev}
spring:
  data:
#    mongodb:
#      uri: mongodb://${AGGREGATION_MONGO_USERNAME}:${AGGREGATION_MONGO_PASSWORD}@${AGGREGATION_MONGO_IPS:localhost:27018}/${AGGREGATION_DB:aggregation_db}?authSource=admin
#      auto-index-creation: true
    mongodb:
      uri: mongodb://localhost:27018/aggregation_db
      auto-index-creation: true
  cloud:
    gcp:
      pubsub:
        enabled: true
    kubernetes:
      config:
        enabled: true
  servlet:
    multipart:
      enabled: true
      max-file-size: 10MB
      max-request-size: 10MB


service:
  rater-quote-service:
    url: ${QUOTE_SERVICE_URL:https://dev-api.submission.concirrusquest.com/rater/quote-service}

slack:
  webhook:
    url: ${AGGREGATION_SLACK_ALERT_URL:*******************************************************************************}

namespace:
  submission: ${SUBMISSION_NAMESPACE:submission-dev}
  currency-service: ${CURRENCY_SERVICE_NAMESPACE:hull-mt-dev}

cleanup:
  threshold: 1

tenant-ids: ${VALID_TENANT_IDS:26a926c5-2e2d-40bf-950b-1dddf41a3746}