package com.concirrus.submissionAdapter.scheduler;


import com.concirrus.submissionAdapter.config.TenantContextHolder;
import com.concirrus.submissionAdapter.dal.AggregationJobRepository;
import com.concirrus.submissionAdapter.dal.LocationTaskRepository;
import com.concirrus.submissionAdapter.dto.enums.ProcessingStatus;
import com.concirrus.submissionAdapter.model.AggregationJob;
import com.concirrus.submissionAdapter.service.TenantService;
import com.concirrus.submissionAdapter.utils.SlackNotifier;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;

@Component
@Slf4j
@RequiredArgsConstructor
public class CleanUpScheduler {

    private final SlackNotifier slackNotifier;

    private final AggregationJobRepository aggregationJobRepository;
    private final LocationTaskRepository locationTaskRepository;
    private final TenantService tenantService;
    @Value("${cleanup.threshold:30}")
    private Long cleanUpThreshold;

    @Scheduled(cron = "0 0 6 * * *")
    @SchedulerLock(name = "clean_up_scheduler", lockAtLeastFor = "5m", lockAtMostFor = "10m")
    public void cleanUp(){
        try {
            tenantService.getValidTenantIds().forEach(tenantId -> {
                String tenantAlias = tenantService.getTenantAlias(tenantId);
                TenantContextHolder.setTenantContext(tenantId, tenantAlias);
                slackNotifier.sendAlert("Starting clean up job for tenant " + tenantId);
                cleanUpOldJobs();
                TenantContextHolder.clear();
                slackNotifier.sendAlert("Clean up job completed for tenant " + tenantId);
            });
        }catch (Exception e){
            log.error("Error occurred while cleaning up old jobs: {}", e.getMessage(), e);
            slackNotifier.sendAlert("Error occurred while cleaning up old jobs: " + e.getMessage());
        } finally {
            TenantContextHolder.clear();
        }

    }

    private void cleanUpOldJobs(){
        List<AggregationJob> oldJobs = aggregationJobRepository.findByStatusAndUpdatedAtBefore(ProcessingStatus.COMPLETED,Instant.now().minus(cleanUpThreshold, ChronoUnit.DAYS));
        log.info("Found {} old jobs to clean up", oldJobs.size());
        if (oldJobs.isEmpty()) {
            log.info("No old jobs found to clean up");
            return;
        }
        oldJobs.forEach(job -> {
            log.info("Deleting job {} and its tasks", job.getJobId());
            locationTaskRepository.deleteByJobId(job.getJobId());
        });
        aggregationJobRepository.deleteAll(oldJobs);
    }

}
