package com.concirrus.submissionAdapter.scheduler;

import com.concirrus.submissionAdapter.config.TenantContextHolder;
import com.concirrus.submissionAdapter.service.TenantService;
import com.concirrus.submissionAdapter.service.politicalViolence.RecomputationService;
import com.concirrus.submissionAdapter.utils.SlackNotifier;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Slf4j
@Component
public class MonthlyScheduler {

    private final RecomputationService recomputationService;
    private final SlackNotifier slackNotifier;
    private final TenantService tenantService;

    @Scheduled(cron = "0 0 0 1 * *")
    @SchedulerLock(name = "recomputation_scheduler", lockAtLeastFor = "15m", lockAtMostFor = "30m")
    public void monthlyTask(){
        try {
            tenantService.getValidTenantIds().forEach(tenantId -> {
                try {
                    String tenantAlias = tenantService.getTenantAlias(tenantId);
                    TenantContextHolder.setTenantContext(tenantId, tenantAlias);
                    log.info("Running monthly task for recalculating monetary values for tenant {}", tenantId);
                    slackNotifier.sendAlert("Starting monthly recalculation of monetary values using updated exchange rates for tenant " + tenantId);

                    recomputationService.recomputeAll();
                    slackNotifier.sendAlert("Monthly recalculation completed successfully for tenant " + tenantId);
                } catch (Exception e) {
                    log.error("Monthly task failed for tenant {}: {}", tenantId, e.getMessage(), e);
                    slackNotifier.sendAlert("❌ Monthly recalculation failed for tenant " + tenantId + ": " + e.getMessage());
                } finally {
                    TenantContextHolder.clear();
                }
            });
        } catch (Exception e) {
            log.error("Error occurred while running monthly task: {}", e.getMessage(), e);
            slackNotifier.sendAlert("Error occurred while running monthly task: " + e.getMessage());
        } finally {
            TenantContextHolder.clear();
        }
    }
}
