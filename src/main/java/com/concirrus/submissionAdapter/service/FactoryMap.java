package com.concirrus.submissionAdapter.service;

import com.concirrus.submissionAdapter.dto.enums.ProductType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class FactoryMap {

    private final Map<ProductType, MessageHandlerFactory> factoryMap;
    @Autowired
    public FactoryMap(List<MessageHandlerFactory> factoryList) {
        factoryMap = factoryList.stream()
                .collect(Collectors.toMap(MessageHandlerFactory::getProductType, Function.identity()));

    }
    public MessageHandlerFactory getFactory(ProductType type) {
        return factoryMap.get(type);
    }

}
