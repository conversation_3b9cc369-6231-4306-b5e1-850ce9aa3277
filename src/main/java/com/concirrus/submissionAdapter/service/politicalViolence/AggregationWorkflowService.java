package com.concirrus.submissionAdapter.service.politicalViolence;

import com.concirrus.submissionAdapter.config.TenantContextHolder;
import com.concirrus.submissionAdapter.dal.AccountRepository;
import com.concirrus.submissionAdapter.dal.AggregationJobRepository;
import com.concirrus.submissionAdapter.dal.LocationRepository;
import com.concirrus.submissionAdapter.dal.LocationTaskRepository;
import com.concirrus.submissionAdapter.dto.enums.ProcessingStatus;
import com.concirrus.submissionAdapter.dto.enums.UpdateType;
import com.concirrus.submissionAdapter.dto.miAnalysis.MiAnalysisJobRequest;
import com.concirrus.submissionAdapter.dto.workflow.LocationEventDTO;
import com.concirrus.submissionAdapter.dto.workflow.TaskCallBack;
import com.concirrus.submissionAdapter.eventing.producer.EventSender;
import com.concirrus.submissionAdapter.model.Account;
import com.concirrus.submissionAdapter.model.AggregationJob;
import com.concirrus.submissionAdapter.model.Location;
import com.concirrus.submissionAdapter.model.LocationTask;
import com.concirrus.submissionAdapter.sal.BlastZoneProcessorSal;
import com.concirrus.submissionAdapter.service.TenantService;
import com.concirrus.submissionAdapter.utils.SlackNotifier;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

import static com.concirrus.submissionAdapter.dto.enums.UpdateType.QUOTE_CREATED;
import static com.concirrus.submissionAdapter.dto.enums.UpdateType.QUOTE_UPDATED;

@Service
@Slf4j
@RequiredArgsConstructor
public class AggregationWorkflowService {

    private final AggregationJobRepository jobRepository;
    private final LocationRepository locationRepository;
    private final LocationTaskRepository taskRepository;
    private final AccountRepository accountRepository;
    private final BlastZoneProcessorSal blastZoneProcessorSal;
    private final EventSender eventSender;
    private final SlackNotifier slackNotifier;
    private final TenantService tenantService;


    public void createTasksAndSendEvents(AggregationJob aggregationJob, UpdateType updateType, List<String> locationIds) {
        if(aggregationJob == null){
            log.error("Aggregation job is null. Skipping task creation and event sending.");
            return;
        }
        String submissionId = aggregationJob.getSubmissionId();
        String jobId = aggregationJob.getJobId();

        log.info("Creating tasks for job {} for submission {}", jobId, submissionId);

        int totalProcessed = processLocationsInBatches(submissionId, jobId, updateType, locationIds);
        aggregationJob.setCountOfLocations(totalProcessed);
    }

    /**
     * Handles callback from BlastZone processor
     */

    @Transactional
    public void handleTaskCallback(TaskCallBack callback) {
        String jobId = callback.getJobId();
        String locationId = callback.getLocationId();
        var status = callback.getStatus();
        log.info("Received callback for location {} in job {}: {}", locationId, jobId, status);

        // If locationId is null or empty, this is a code-level event (not location-specific)
        if (locationId == null || locationId.isEmpty()) {
            if (ProcessingStatus.COMPLETED.name().equalsIgnoreCase(status.name())) {
                checkAndUpdateJobStatus(jobId);
                log.info("Job {} marked as COMPLETED by code-level callback", jobId);
            } else {
                log.warn("Received code-level callback for job {} with non-completed status: {}", jobId, status);
            }
            return;
        }

        // Update task status using jobId and locationId
        LocationTask task = taskRepository.findByJobIdAndLocationId(jobId, locationId)
                .orElseThrow(() -> new RuntimeException("Task not found for jobId: " + jobId +
                        " and locationId: " + locationId));

        task.setStatus(status);
        task.setUpdatedOn(Instant.now());
        taskRepository.save(task);

        log.info("Updated task for location {} in job {} status to {}", locationId, jobId, status);

        // Check if all tasks in the job are completed
        checkAndUpdateJobStatus(jobId);
    }


    @Async
    public void createMiAnalysisJob(AggregationJob job) {
        if(TenantContextHolder.getTenantId() == null){
            log.error("Tenant ID is null. picking client id from job ");
            TenantContextHolder.setTenantId(job.getClientId());
            TenantContextHolder.setTenantAlias(tenantService.getTenantAlias(job.getClientId()));
        }
        String submissionId = job.getSubmissionId();
        List<Account> accountsToProcess = new ArrayList<>();
        if (QUOTE_CREATED.equals(job.getUpdateType())||QUOTE_UPDATED.equals(job.getUpdateType())){
            Optional<Account>optional = accountRepository.findBySubmissionIdAndQuoteId(submissionId, job.getQuoteId());
            accountsToProcess = optional.map(List::of).orElseGet(ArrayList::new);
        }else {
            accountsToProcess = accountRepository.findBySubmissionId(submissionId);
        }
        if (accountsToProcess.isEmpty()) {
            log.info("No accounts found for submission {}. Skipping MI analysis job creation.", submissionId);
            return;
        }

        List<MiAnalysisJobRequest> miAnalysisJobRequests = accountsToProcess.stream().filter(Account::isValidAccount).map(account -> {
            MiAnalysisJobRequest request = new MiAnalysisJobRequest();
            request.setSubmissionId(account.getSubmissionId());
            request.setQuoteId(account.getQuoteId());
            request.setSAndT(account.getLimit());
            request.setPerils(Collections.singletonList("sAndT"));
            request.setExcess(account.getExcess());
            request.setLine(account.getWrittenLine());
            request.setDeductible(account.getDeductible());
            request.setInitiateJob(false);
            return request;
        }).toList();

        if (miAnalysisJobRequests.isEmpty()) {
            log.info("No valid accounts found for submission {}. Skipping MI analysis job creation.", submissionId);
            return;
        }
        log.info("Creating MI analysis jobs for {} accounts", miAnalysisJobRequests.size());

       List<String> miAnalysisJobIds = blastZoneProcessorSal.createMiAnalysisJobs(miAnalysisJobRequests);
        job.setMiAnalysisJobIds(miAnalysisJobIds);
        jobRepository.save(job);
    }


    private int processLocationsInBatches(String submissionId, String jobId, UpdateType updateType, List<String> locationIds) {
        final int pageSize = 100;
        int page = 0;
        int totalProcessed = 0;

        while (true) {
            Pageable pageable = PageRequest.of(page, pageSize);
            Page<Location> locationPage = getLocationPage(submissionId, locationIds, pageable);

            if (locationPage.isEmpty()) {
                break;
            }

            List<Location> locations = locationPage.getContent();
            processBatch(locations, submissionId, jobId, updateType);

            totalProcessed += locations.size();
            page++;
        }

        log.info("Processed {} locations for job {}", totalProcessed, jobId);
        return totalProcessed;
    }

    private Page<Location> getLocationPage(String submissionId, List<String> locationIds, Pageable pageable) {
        if (locationIds != null && !locationIds.isEmpty()) {
            return locationRepository.findByIdIn(locationIds, pageable);
        } else {
            return locationRepository.findBySubmissionId(submissionId, pageable);
        }
    }

    private void processBatch(List<Location> locations, String submissionId, String jobId, UpdateType updateType) {
        List<LocationTask> tasks = createTasksFromLocations(locations, jobId, updateType);
        List<LocationEventDTO> events = createEventsFromLocations(locations, submissionId, jobId, updateType);

        // Send events and save tasks for this batch
        eventSender.sendLocationEvents(events);
        taskRepository.saveAll(tasks);
    }

    private List<LocationTask> createTasksFromLocations(List<Location> locations, String jobId, UpdateType updateType) {
        return locations.stream()
                .map(location -> LocationTask.builder()
                        .jobId(jobId)
                        .locationId(location.getId())
                        .status(ProcessingStatus.IN_PROGRESS)
                        .taskId(UUID.randomUUID().toString())
                        .updateType(updateType)
                        .createdOn(Instant.now())
                        .build())
                .collect(Collectors.toList());
    }

    private List<LocationEventDTO> createEventsFromLocations(List<Location> locations, String submissionId, String jobId, UpdateType updateType) {
        return locations.stream()
                .map(location -> LocationEventDTO.builder()
                        .submissionId(submissionId)
                        .locationId(location.getId())
                        .longitude(location.getLongitude())
                        .latitude(location.getLatitude())
                        .state(location.getState())
                        .updateType(updateType)
                        .jobId(jobId)
                        .clientId(TenantContextHolder.getTenantId())
                        .build())
                .collect(Collectors.toList());
    }



    private void checkAndUpdateJobStatus(String jobId) {
        long inProgressCount = taskRepository.countByJobIdAndStatus(jobId, ProcessingStatus.IN_PROGRESS);

        if (inProgressCount == 0) {
            // All tasks are completed, update job status
            AggregationJob job = jobRepository.findByJobId(jobId)
                    .orElseThrow(() -> new RuntimeException("Job not found: " + jobId));

            job.setStatus(ProcessingStatus.COMPLETED);
            job.setUpdatedAt(Instant.now());
            jobRepository.save(job);

            log.info("Job {} marked as COMPLETED - all tasks finished", jobId);

            notifyJobCompletion(job);
        } else {
            log.info("Job {} still has {} tasks in progress", jobId, inProgressCount);
        }
    }

    private void notifyJobCompletion(AggregationJob job) {
        log.info("Job completion notification for job {}: submissionId={}, updateType={}",
                job.getJobId(), job.getSubmissionId(), job.getUpdateType());
        //slackNotifier.sendAlert("Job "+ job.getJobId()+"of type " + job.getUpdateType() + " for submission " + job.getSubmissionId() + " completed: " );
        triggerMiAnalysis(job);
    }

    private void triggerMiAnalysis(AggregationJob job) {
        try {
            List<String> miAnalysisJobIds = job.getMiAnalysisJobIds();

            if (miAnalysisJobIds == null||miAnalysisJobIds.isEmpty()) {
               log.info("No MI analysis job ids found for job {}. Skipping MI analysis.", job.getJobId());
                return;
            }
            blastZoneProcessorSal.triggerMiAnalysisJobs(miAnalysisJobIds);


        } catch (Exception e) {
            log.error("Error triggering MI analysis for job {}: {}", job.getJobId(), e.getMessage(), e);
        }


    }


}
