package com.concirrus.submissionAdapter.service.politicalViolence;

import com.concirrus.submissionAdapter.dal.AccountRepository;
import com.concirrus.submissionAdapter.dal.AggregationJobRepository;
import com.concirrus.submissionAdapter.dal.LocationDal;
import com.concirrus.submissionAdapter.dal.LocationRepository;
import com.concirrus.submissionAdapter.dto.currencyExchange.LocationUpdateResult;
import com.concirrus.submissionAdapter.dto.enums.ProcessingStatus;
import com.concirrus.submissionAdapter.dto.enums.ProductType;
import com.concirrus.submissionAdapter.dto.enums.State;
import com.concirrus.submissionAdapter.dto.enums.UpdateType;
import com.concirrus.submissionAdapter.dto.submission.Quote;
import com.concirrus.submissionAdapter.dto.submission.Submission;
import com.concirrus.submissionAdapter.dto.workflow.SubmissionUpdateEvent;
import com.concirrus.submissionAdapter.model.Account;
import com.concirrus.submissionAdapter.model.AggregationJob;
import com.concirrus.submissionAdapter.sal.BlastZoneProcessorSal;
import com.concirrus.submissionAdapter.sal.QuoteServiceSal;
import com.concirrus.submissionAdapter.sal.SubmissionHandlerSal;
import com.concirrus.submissionAdapter.service.MessageHandlerFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static com.concirrus.submissionAdapter.constants.Constants.POLITICAL_VIOLENCE;
import static com.concirrus.submissionAdapter.dto.enums.UpdateType.SUBMISSION_DELETED;
import static com.concirrus.submissionAdapter.dto.enums.UpdateType.SUBMISSION_REVIEWED;

@Service
@Slf4j
@RequiredArgsConstructor
public class AviationWarMessageHandler implements MessageHandlerFactory {

    private final AccountRepository accountRepository;
    private final LocationRepository locationRepository;
    private final SubmissionHandlerSal submissionHandlerSal;
    private final QuoteServiceSal quoteServiceSal;
    private final AggregationWorkflowService aggregationWorkflowService;
    private final LocationDal locationDal;
    private final AggregationJobRepository aggregationJobRepository;
    private final PoliticalViolenceUtils politicalViolenceUtils;
    private final BlastZoneProcessorSal blastZoneProcessorSal;

    @Override
    public ProductType getProductType() {
        return ProductType.AVIATION_WAR;
    }

    public void processSubmissionUpdate(SubmissionUpdateEvent submissionUpdateEvent) {
        String submissionId = submissionUpdateEvent.getSubmissionId();
        String clientId = submissionUpdateEvent.getClientId();

        log.info("Processing submission update event: {} for submission: {}", submissionUpdateEvent.getUpdateType(), submissionId);

        try {
            // Validate and parse update type first (fail fast)
            UpdateType updateType = parseUpdateType(submissionUpdateEvent.getUpdateType());
            if (updateType == null) {
                log.error("Invalid update type received: {}. Skipping event processing.",
                        submissionUpdateEvent.getUpdateType());
                throw new IllegalArgumentException("Invalid update type: " + submissionUpdateEvent.getUpdateType());
            }
            Submission submission = null;
            // Fetch submission
            if(!SUBMISSION_DELETED.equals(updateType)) {
                submission = submissionHandlerSal.getSubmissionById(submissionId, clientId);

                // Check if submission should be processed
                if (shouldSkipSubmission(submission, submissionId)) {
                    return;
                }
            }

            // Create aggregation job
            AggregationJob aggregationJob = createAggregationJob(submissionUpdateEvent, updateType);

            // Process the update
            processUpdateByType(submission, aggregationJob, updateType, submissionId);

            // Save aggregation job
            aggregationJobRepository.save(aggregationJob);

            log.info("Successfully processed submission update: {} for submission: {}", updateType, submissionId);

        }catch (IllegalArgumentException e) {
            log.error("Invalid argument while processing submission update for submission: {}", submissionId, e);
            throw e;
        }
        catch (Exception e) {
            // Unexpected exceptions - log and wrap
            log.error("Unexpected error processing submission update for submission: {}", submissionId, e);
            throw new RuntimeException("Failed to process submission update for: " + submissionId, e);
        }
    }

    private boolean shouldSkipSubmission(Submission submission, String submissionId) {
        if (submission == null) {
            log.error("Failed to fetch submission with ID {}. Skipping event processing.", submissionId);
            throw new RuntimeException("Submission not found: " + submissionId);
        }
        if (submission.getState() == State.INBOX) {
            log.info("Submission {} is in INBOX state. Skipping event processing.", submissionId);
            return true;
        }

        if (!POLITICAL_VIOLENCE.equalsIgnoreCase(submission.getLineOfBusiness())) {
            log.info("Submission {} has line of business {}. Only processing POLITICAL_VIOLENCE. Skipping event processing.",
                    submissionId, submission.getCoverageType());
            return true;
        }

        return false;
    }

    private AggregationJob createAggregationJob(SubmissionUpdateEvent event, UpdateType updateType) {
        return AggregationJob.builder()
                .jobId(UUID.randomUUID().toString())
                .submissionId(event.getSubmissionId())
                .clientId(event.getClientId())
                .updateType(updateType)
                .quoteId(event.getQuoteId())
                .createdAt(Instant.now()) // Add timestamp if available
                .build();
    }

    private void processUpdateByType(Submission submission, AggregationJob aggregationJob, UpdateType updateType, String submissionId) {
        try {
            switch (updateType) {
                // SUBMISSION Level Events
                case SUBMISSION_REVIEWED -> {
                    log.debug("Processing submission reviewed for: {}", submissionId);
                    processSubmissionReviewed(submission, aggregationJob);
                }
                case SUBMISSION_DELETED -> {
                    log.debug("Processing submission deleted for: {}", submissionId);
                    processDeletedSubmission(submission, aggregationJob);
                }
                case SUBMISSION_BOUND -> {
                    log.debug("Processing submission bound for: {}", submissionId);
                    processBoundSubmission(submission, aggregationJob);
                }

                // QUOTE Level Events
                case QUOTE_CREATED -> {
                    log.debug("Processing quote created for: {}", submissionId);
                    processQuoteCreated(submission, aggregationJob);
                }
                case QUOTE_DELETED -> {
                    log.debug("Processing quote deleted for: {}", submissionId);
                    processQuoteDeleted(submission, aggregationJob);
                }
                case QUOTE_UPDATED -> {
                    log.debug("Processing quote updated for: {}", submissionId);
                    processQuoteUpdated(submission, aggregationJob);
                }

                // LOCATION Level Events
                case LOCATION_UPLOADED -> {
                    log.debug("Processing location uploaded for: {}", submissionId);
                    processLocationUploaded(submission, aggregationJob);
                }
                case LOCATION_UPDATED -> {
                    log.debug("Processing location updated for: {}", submissionId);
                    processLocationUpdated(submission, aggregationJob);
                }

                default -> {
                    log.warn("Unhandled update type: {} for submission: {}", updateType, submissionId);
                    // Consider throwing an exception here if unhandled types should not be ignored
                }
            }
        } catch (Exception e) {
            log.error("Error occurred while processing update type {} for submission {}: {}", updateType, submissionId, e.getMessage(), e);
            aggregationJob.setStatus(ProcessingStatus.FAILED);
            aggregationJob.setComment("Failed to process update type: " + updateType + " Error: " + e.getMessage());
            aggregationJobRepository.save(aggregationJob);
            throw new RuntimeException("Failed to process update type: " + updateType, e);
        }
    }

    private UpdateType parseUpdateType(String updateTypeStr) {
        if (updateTypeStr == null || updateTypeStr.trim().isEmpty()) {
            log.error("Update type is null or empty");
            return null;
        }

        try {
            return UpdateType.valueOf(updateTypeStr.trim().toUpperCase());
        } catch (IllegalArgumentException e) {
            log.error("Invalid update type: {}. Valid values are: {}", updateTypeStr,
                    java.util.Arrays.toString(UpdateType.values()));
            return null;
        }
    }

    private void processSubmissionReviewed(Submission submission, AggregationJob aggregationJob ) {
        String submissionId = aggregationJob.getSubmissionId();
        String clientId = aggregationJob.getClientId();
        log.info("Starting ingestion process for submission : {}", submissionId);

        boolean accountExists = accountRepository.existsBySubmissionId(submissionId);
        if (accountExists) {
            log.info("Account already exists for submission {}", submissionId);
            accountRepository.deleteBySubmissionId(submissionId);
        }
        if (locationRepository.existsBySubmissionId(submissionId)) {
            log.info("Deleting existing locations for submission {} and sending location deleted events", submissionId);
            locationRepository.deleteBySubmissionId(submissionId);
            boolean cleared = blastZoneProcessorSal.clearSubmissionData(submissionId);
            if (!cleared) {
                /* we can do this because No account in review state can influence blast zones we only need to create new location blast zone mappings so it's safe to delete old ones in one go and save computation time */
                log.error("Failed to clear submission data for submission {} for submission reviewed event will reprocess blast zones to via location deleted events", submissionId);
                aggregationWorkflowService.createTasksAndSendEvents(aggregationJob,UpdateType.LOCATION_DELETED,null);
            }

        }


        List<Quote> quotes = quoteServiceSal.getAllQuotesBySubmissionId(submissionId, clientId);
        if (quotes != null && !quotes.isEmpty()) {

            for (Quote quote : quotes) {
                try {
                    politicalViolenceUtils.ingestQuote(submission, quote);
                } catch (Exception e) {
                    log.error("Error processing quote {} for submission {}: {}", quote.getQuoteId(), submissionId,
                            e.getMessage(), e);
                }
            }
        }
        int countOfLocations = politicalViolenceUtils.ingestLocations(submissionId, clientId,submission.getState(),UpdateType.SUBMISSION_REVIEWED);
        if (countOfLocations == 0) {
            log.info("No locations found for submission {}. Skipping event sending.", submissionId);
            aggregationJob.setCountOfLocations(0);
            aggregationJob.setStatus(ProcessingStatus.COMPLETED);
            aggregationJob.setComment("No locations found for submission " + submissionId);
            aggregationJobRepository.save(aggregationJob);
            return;
        }
        log.info("Successfully ingested {} locations for submission {}, sending location created events", countOfLocations, submissionId);
        aggregationWorkflowService.createMiAnalysisJob(aggregationJob);
        aggregationWorkflowService.createTasksAndSendEvents(aggregationJob, SUBMISSION_REVIEWED,null);
    }

    private void processDeletedSubmission(Submission submission, AggregationJob aggregationJob) {
        try {
            String submissionId = aggregationJob.getSubmissionId();
            List<Account> accounts = accountRepository.findBySubmissionId(submissionId);
            if (!accounts.isEmpty()) {
                accountRepository.deleteAll(accounts);
            }
            aggregationWorkflowService.createTasksAndSendEvents(aggregationJob, SUBMISSION_DELETED,null);
            locationRepository.deleteBySubmissionId(submissionId);
        } catch (Exception e) {
            log.error("Error occurred while processing deleted submission {}: {}", aggregationJob.getSubmissionId(), e.getMessage(), e);
            throw new RuntimeException("Failed to process deleted submission: " + aggregationJob.getSubmissionId(), e);
        }
    }

    private void processBoundSubmission(Submission submission, AggregationJob aggregationJob ) {
        try {
            String submissionId = aggregationJob.getSubmissionId();

            List<Quote> quotes = quoteServiceSal.getAllQuotesBySubmissionId(submissionId, submission.getClientId());

            if (quotes != null && !quotes.isEmpty()) {
                for (Quote quote : quotes) {
                    Optional<Account> optional = accountRepository.findBySubmissionIdAndQuoteId(submissionId, quote.getQuoteId());
                    Account account = optional.orElseGet(Account::new);
                    if (optional.isEmpty()){
                        //Need to fetch account submission
                        account = politicalViolenceUtils.ingestQuote(submission, quote);
                    }
                    account.setState(quote.getState());
                    accountRepository.save(account);
                }
            }


            locationDal.updateLocationState(submissionId, State.BOUND);
            aggregationWorkflowService.createTasksAndSendEvents(aggregationJob,UpdateType.SUBMISSION_BOUND,null);

        } catch (Exception e) {
            log.error("Error occurred while processing bound submission {}: {}", submission.getSubmissionId(), e.getMessage(), e);
            throw new RuntimeException("Failed to process bound submission: " + submission.getSubmissionId(), e);
        }
    }


    private void processQuoteCreated(Submission submission, AggregationJob aggregationJob) {

        String quoteId = aggregationJob.getQuoteId();
        String clientId = submission.getClientId();
        Quote quote = quoteServiceSal.getQuoteById(quoteId, clientId);
        if (quote == null) {
            throw new RuntimeException("Quote not found: " + quoteId);
        }

        politicalViolenceUtils.ingestQuote(submission, quote);
        aggregationWorkflowService.createMiAnalysisJob(aggregationJob);
        aggregationWorkflowService.createTasksAndSendEvents(aggregationJob,UpdateType.QUOTE_CREATED,null);
    }

    private void processQuoteDeleted(Submission submission, AggregationJob aggregationJob) {
        String submissionId = submission.getSubmissionId();
        String quoteId = aggregationJob.getQuoteId();
        Optional<Account> optional = accountRepository.findBySubmissionIdAndQuoteId(submissionId, quoteId);
        if (optional.isEmpty()) {
            log.info("No account found for quote {}. Skipping quote deleted processing.", quoteId);
            aggregationJob.setStatus(ProcessingStatus.COMPLETED);
            aggregationJob.setUpdatedAt(Instant.now());
            aggregationJob.setCountOfLocations(0);
            aggregationJob.setComment("No account found for quote " + quoteId);
            aggregationJobRepository.save(aggregationJob);
            return;
        }
        optional.ifPresent(accountRepository::delete);

        aggregationWorkflowService.createTasksAndSendEvents(aggregationJob,UpdateType.QUOTE_DELETED,null);

    }

    private void processQuoteUpdated(Submission submission, AggregationJob aggregationJob) {

        String submissionId = aggregationJob.getSubmissionId();
        String quoteId = aggregationJob.getQuoteId();
        String clientId = submission.getClientId();
        Optional<Account> optional = accountRepository.findBySubmissionIdAndQuoteId(submissionId, quoteId);
        Account account = optional.orElseGet(Account::new);
        boolean shouldSendEvent = true;
        Quote updatedQuote = quoteServiceSal.getQuoteById(quoteId, clientId);
        if (updatedQuote == null) {
            throw new RuntimeException("Updated quote not found: " + quoteId);
        }
        if (optional.isEmpty()) {
            log.info("No existing account found for quote {}. Ingested new account.", quoteId);
        }else {
            shouldSendEvent = politicalViolenceUtils.haveRelevantFieldsChangedForQuote(account, updatedQuote, submission);

        }
        politicalViolenceUtils.ingestQuote(submission, updatedQuote);


        if (shouldSendEvent) {
            //TODO: should only trigger MI Analysis
            aggregationWorkflowService.createMiAnalysisJob(aggregationJob);
            aggregationWorkflowService.createTasksAndSendEvents(aggregationJob,UpdateType.QUOTE_UPDATED,null);
        } else {
            log.info("No relevant field changes detected for quote {}. Skipping event sending.", quoteId);
            aggregationJob.setStatus(ProcessingStatus.COMPLETED);
            aggregationJob.setUpdatedAt(Instant.now());
            aggregationJob.setCountOfLocations(0);
            aggregationJob.setComment("No relevant field changes detected for quote " + quoteId);
            aggregationJobRepository.save(aggregationJob);
        }

    }


    private void processLocationUploaded(Submission submission, AggregationJob aggregationJob) {
        String submissionId = submission.getSubmissionId();
        String clientId = submission.getClientId();

        Optional<Account>optional = accountRepository.findFirstBySubmissionId(submissionId);
        if (optional.isEmpty()) {
            log.info("No account found for submissionId {}. Skipping location upload processing.", submissionId);
            return;
        }
        Account account = optional.get();
        // Delete all locations for the submissionId
        locationRepository.deleteBySubmissionId(submissionId);
        /* we can do this because Location Upload is only supported in states below BOUND and No account in these state can influence blast zones we only need to create new location blast zone mappings so it's safe to delete old ones in one go and save computation time */
        boolean cleared = blastZoneProcessorSal.clearSubmissionData(submissionId);
        if (!cleared) {
            log.error("Failed to clear submission data for submission {} for location upload event will reprocess blast zones to via location deleted events", submissionId);
            aggregationWorkflowService.createTasksAndSendEvents(aggregationJob,UpdateType.LOCATION_DELETED,null);
        }
        // Ingest new locations from handler service
        int totalLocations = politicalViolenceUtils.ingestLocations(submissionId, clientId,account.getState(),UpdateType.LOCATION_CREATED);
        if (totalLocations == 0) {
            log.info("No locations found for submission {}. Skipping event .", submissionId);
            aggregationJob.setCountOfLocations(0);
            aggregationJob.setStatus(ProcessingStatus.COMPLETED);
            aggregationJob.setComment("No locations found for submission " + submissionId);
            aggregationJobRepository.save(aggregationJob);
            return;
        }
        aggregationJob.setCountOfLocations(totalLocations);
        aggregationWorkflowService.createMiAnalysisJob(aggregationJob);
        aggregationWorkflowService.createTasksAndSendEvents(aggregationJob,UpdateType.LOCATION_CREATED,null);
    }

    private void processLocationUpdated(Submission submission, AggregationJob aggregationJob) {
        String submissionId = submission.getSubmissionId();
        String clientId = submission.getClientId();
        log.info("Processing location updated event for submission: {}", submissionId);

        LocationUpdateResult updateResult = politicalViolenceUtils.processAllLocationPages(submissionId, clientId);
        boolean hasUpdates = updateResult.hasCoordinateUpdates() || updateResult.hasDataUpdates();
        if (!hasUpdates) {
            log.info("No relevant updates detected in locations for submission {}. Skipping event sending.", submissionId);
            aggregationJob.setStatus(ProcessingStatus.COMPLETED);
            aggregationJob.setUpdatedAt(Instant.now());
            aggregationJob.setCountOfLocations(0);
            aggregationJob.setComment("No relevant updates detected for submission " + submissionId);
            return;
        }

        if (updateResult.hasCoordinateUpdates()) {
            aggregationWorkflowService.createTasksAndSendEvents(
                    aggregationJob,
                    UpdateType.LOCATION_COORDINATES_UPDATED,
                    updateResult.getCoordinateUpdatedLocations()
            );
        }

        if (updateResult.hasDataUpdates()) {
            aggregationWorkflowService.createTasksAndSendEvents(
                    aggregationJob,
                    UpdateType.LOCATION_DATA_UPDATED,
                    updateResult.getDataUpdatedLocations()
            );
        }
        aggregationWorkflowService.createMiAnalysisJob(aggregationJob);
        aggregationJob.setCountOfLocations(updateResult.getTotalUpdatedCount());
    }

}

