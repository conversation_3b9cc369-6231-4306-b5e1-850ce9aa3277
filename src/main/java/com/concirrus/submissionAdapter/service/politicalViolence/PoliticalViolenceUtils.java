package com.concirrus.submissionAdapter.service.politicalViolence;

import com.concirrus.submissionAdapter.dal.AccountDal;
import com.concirrus.submissionAdapter.dal.AccountRepository;
import com.concirrus.submissionAdapter.dal.LocationRepository;
import com.concirrus.submissionAdapter.dto.currencyExchange.LocationUpdateResult;
import com.concirrus.submissionAdapter.dto.enums.State;
import com.concirrus.submissionAdapter.dto.enums.UpdateType;
import com.concirrus.submissionAdapter.dto.submission.InsuredAssetsResponse;
import com.concirrus.submissionAdapter.dto.submission.Quote;
import com.concirrus.submissionAdapter.dto.submission.Submission;
import com.concirrus.submissionAdapter.model.Account;
import com.concirrus.submissionAdapter.model.Location;
import com.concirrus.submissionAdapter.sal.SubmissionHandlerSal;
import com.concirrus.submissionAdapter.utils.CurrencyConverter;
import com.concirrus.submissionAdapter.utils.LocationParser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.concirrus.submissionAdapter.constants.Constants.POLITICAL_VIOLENCE;
import static com.concirrus.submissionAdapter.constants.Constants.USD;

@Component
@Slf4j
@RequiredArgsConstructor
public class PoliticalViolenceUtils {
    private final CurrencyConverter currencyConverter;
    private final SubmissionHandlerSal submissionHandlerSal;
    private final AccountRepository accountRepository;
    private final LocationRepository locationRepository;
    private final AccountDal accountDal;



    public int ingestLocations(String submissionId, String clientId, State state, UpdateType updateType) {
        try {
            boolean hasMore = true;
            int page = 0;
            int pageSize = 50;
            int totalLocations = 0;
            while (hasMore) {
                // Response Entity wrap
                InsuredAssetsResponse response = submissionHandlerSal.getInsuredAssetsForSubmission(submissionId,
                        POLITICAL_VIOLENCE, page, pageSize, clientId);
                if (response.getStatus() != 200) {
                    log.info("Error occurred while fetching page {} of locations for submissionId {}", page,
                            submissionId);
                    throw new RuntimeException("Failed to fetch locations for submission: " + submissionId);
                }
                List<Map<String, Object>> content = response.getResult().getContent();
                log.info("retrieved {} locations", content.size());

                List<Location> locations = content.stream()
                        .map(LocationParser::parseLocation)
                        .filter(Objects::nonNull)
                        .peek(location -> {
                            location.setState(state);
                        })
                        .toList();

                convertLocationValues(locations,clientId);
                locationRepository.saveAll(locations);
                totalLocations += locations.size();
                hasMore = !response.getResult().isLast();
                page++;
            }

            return totalLocations;
        } catch (Exception e) {
            log.error("Error occurred while ingesting locations for submissionId {} ", submissionId, e);
            throw new RuntimeException("Failed to ingest locations for submission: " + submissionId, e);
        }
    }

    public Account ingestQuote(Submission submission, Quote quote) {
        String currency = Quote.getPolicyCurrency(quote);
        Optional<Account> optional = accountRepository.findBySubmissionIdAndQuoteId(submission.getSubmissionId(), quote.getQuoteId());
        Account account;
        account = optional.orElseGet(Account::new);
        Double exchangeRate = currencyConverter.getExchangeRate(currency,submission.getClientId());
        exchangeRate = exchangeRate == null ? 0.0 : exchangeRate;
        account.setAccountName(Submission.getInsuredName(submission));
        account.setBinder(Submission.getCoverType(submission));
        account.setQuoteId(quote.getQuoteId());
        account.setExcess(exchangeRate * Quote.getExcess(quote));
        account.setExcessOriginalCcy(Quote.getExcess(quote));
        account.setExpiryDate(Quote.getExpiryDate(quote));
        account.setInceptionDate(Quote.getInceptionDate(quote));
        account.setLimit(exchangeRate * Quote.getLimit(quote));
        account.setLimitOriginalCcy(Quote.getLimit(quote));
        State state = submission.getState();

        account.setWrittenLine(Quote.getWrittenLine(quote));
        account.setLine(Quote.getSignedLine(quote));
        account.setPolicyReference(Quote.getPolicyReference(quote));
        account.setPolicyCurrency(currency);
        account.setPerils(Quote.getPeril(quote));
        account.setPremium(Quote.getPremiumFromReferencing(quote));
        account.setDeductible(exchangeRate * Quote.getPdDeductible(quote));
        account.setDeductibleOriginalCcy(Quote.getPdDeductible(quote));
        account.setSubmissionId(submission.getSubmissionId());
        account.setLineOfBusiness(submission.getLineOfBusiness());
        account.setState(state);
        return accountDal.createOrUpdateAccount(account);
    }


    public boolean haveRelevantFieldsChangedForQuote(Account account, Quote quote, Submission submission) {
        String currency = Quote.getPolicyCurrency(quote);
        Double exchangeRate = currencyConverter.getExchangeRate(currency,submission.getClientId());
        exchangeRate = exchangeRate == null ? 0.0 : exchangeRate;
        Double newDeductible = exchangeRate * Quote.getPdDeductible(quote);
        Double newExcess = exchangeRate * Quote.getExcess(quote);
        Double newLimit = exchangeRate * Quote.getLimit(quote);
        State state = submission.getState();
        Double newLine;
        if (state != null && state != State.BOUND) {
            newLine = Quote.getWrittenLine(quote);
        } else {
            newLine = Quote.getSignedLine(quote);
        }
        List<String> newPerils = Quote.getPeril(quote);
        boolean perilsChanged = !Objects.equals(account.getPerils(), newPerils);
        boolean changed = !Objects.equals(account.getDeductible(), newDeductible) ||
                !Objects.equals(account.getExcess(), newExcess) ||
                !Objects.equals(account.getLimit(), newLimit) ||
                !Objects.equals(account.getWrittenLine(), newLine) ||
                !Objects.equals(account.getPolicyCurrency(), currency) ||
                perilsChanged;
        log.info("Changed {}", changed);
        return changed;
    }

    public LocationUpdateResult processAllLocationPages(String submissionId, String clientId) {
        LocationUpdateResult result = new LocationUpdateResult();
        int page = 0;
        boolean hasMore = true;

        while (hasMore) {
            log.info("Processing page {} for submission {}", page, submissionId);

            InsuredAssetsResponse response = fetchInsuredAssetsPage(submissionId, clientId, page);

            if (isEmptyResponse(response)) {
                hasMore = false;
                continue;
            }

            List<Map<String, Object>> content = response.getResult().getContent();
            processLocationPage(content, submissionId, result, clientId);

            hasMore = !response.getResult().isLast();
            page++;
        }

        return result;
    }

    public void updateAccountMonetaryValues(List<Account> accounts, Map<String, Double> exchangeRates) {
        accounts.parallelStream().forEach(account -> {
            double exchangeRate = exchangeRates.get(account.getPolicyCurrency())!= null ? exchangeRates.get(account.getPolicyCurrency()) : 1.0;
            account.setExcess(account.getExcessOriginalCcy() * exchangeRate);
            account.setLimit(account.getLimitOriginalCcy() * exchangeRate);
            account.setDeductible(account.getDeductibleOriginalCcy() * exchangeRate);

        });
        accountRepository.saveAll(accounts);
    }
    public void updateLocationMonetaryValues(List<Location> locations, String clientId) {
        convertLocationValues(locations, clientId);
        locationRepository.saveAll(locations);
    }

    private InsuredAssetsResponse fetchInsuredAssetsPage(String submissionId, String clientId, int page) {
        final int PAGE_SIZE = 100;
        return submissionHandlerSal.getInsuredAssetsForSubmission(
                submissionId, POLITICAL_VIOLENCE, page, PAGE_SIZE, clientId
        );
    }

    private boolean isEmptyResponse(InsuredAssetsResponse response) {
        return response == null ||
                response.getResult() == null ||
                response.getResult().getContent() == null ||
                response.getResult().getContent().isEmpty();
    }

    private void processLocationPage(List<Map<String, Object>> content, String submissionId, LocationUpdateResult result, String clientId) {
        List<String> apiLocationIds = extractLocationIds(content);
        Map<String, Location> dbLocationsMap = getExistingLocations(apiLocationIds);
        List<Location> locationsToUpdate = new ArrayList<>();

        for (Map<String, Object> locationData : content) {
            Location apiLocation = LocationParser.parseLocation(locationData);
            if (apiLocation == null) {
                log.warn("Failed to parse location data for submission {}", submissionId);
                continue;
            }

            Location dbLocation = dbLocationsMap.get(apiLocation.getId());
            State state = dbLocation != null && dbLocation.getState() != null ? dbLocation.getState() : State.IN_REVIEW; // fallback to IN_REVIEW if state is null
            apiLocation.setSubmissionId(submissionId);
            apiLocation.setState(state);
            locationsToUpdate.add(apiLocation);

            if (dbLocation == null) {
                log.info("New location {} added to submission {}", apiLocation.getId(), submissionId);
                result.addCoordinateUpdate(apiLocation.getId());
            } else {
                processExistingLocation(dbLocation, apiLocation, submissionId, result);
            }
        }

        if (!locationsToUpdate.isEmpty()) {
            convertLocationValues(locationsToUpdate, clientId);
            locationRepository.saveAll(locationsToUpdate);
        }
    }

    private List<String> extractLocationIds(List<Map<String, Object>> content) {
        return content.stream()
                .map(locationData -> (String) locationData.get("id"))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private Map<String, Location> getExistingLocations(List<String> apiLocationIds) {
        if (apiLocationIds.isEmpty()) {
            return Collections.emptyMap();
        }

        return locationRepository
                .findAllById(apiLocationIds)
                .stream()
                .collect(Collectors.toMap(Location::getId, Function.identity()));
    }

    private void processExistingLocation(Location dbLocation, Location apiLocation, String submissionId, LocationUpdateResult result) {
        boolean hasTivChanged = hasTivChanged(dbLocation, apiLocation);
        boolean coordinatesChanged = hasCoordinatesChanged(dbLocation, apiLocation);

        if (hasTivChanged || coordinatesChanged) {
            if (coordinatesChanged) {
                result.addCoordinateUpdate(apiLocation.getId());
            } else {
                result.addDataUpdate(apiLocation.getId());
            }
            log.info("Location {} marked for update in submission {}", apiLocation.getId(), submissionId);
        }
    }

    /**
     * Checks if coordinates (latitude/longitude) have changed between database and API location
     */
    private boolean hasCoordinatesChanged(Location dbLocation, Location apiLocation) {
        return !Objects.equals(dbLocation.getLatitude(), apiLocation.getLatitude()) ||
                !Objects.equals(dbLocation.getLongitude(), apiLocation.getLongitude());
    }

    /**
     * Checks if S&T values have changed between database and API location
     */
    private boolean hasTivChanged(Location dbLocation, Location apiLocation) {
        return !Objects.equals(dbLocation.getBiValue12MonthsOriginalCcy(), apiLocation.getBiValue12MonthsOriginalCcy()) ||
                !Objects.equals(dbLocation.getBuildingValueOriginalCcy(), apiLocation.getBuildingValueOriginalCcy()) ||
                !Objects.equals(dbLocation.getContentsValueOriginalCcy(), apiLocation.getContentsValueOriginalCcy());
    }


    private void convertLocationValues(List<Location> locations,String clientId) {
        List<String> currencies = locations.stream()
                .map(Location::getLocationCurrency)
                .distinct()
                .filter(Objects::nonNull)
                .filter(currency -> !USD.equalsIgnoreCase(currency))
                .toList();
        Map<String,Double> exchangeRates = currencyConverter.getExchangeRates(currencies,clientId);
        locations.forEach(location -> {
            String currency = location.getLocationCurrency();
            Double exchangeRate;
            if (StringUtils.isEmpty(currency)) {
               exchangeRate = 0.0;
            }else{
                exchangeRate  = exchangeRates.getOrDefault(currency, 0.0);
            }

            log.debug("Exchange rate for currency {} is {}", currency, exchangeRate);
            location.setBiValue12Months(location.getBiValue12MonthsOriginalCcy() * exchangeRate);
            location.setBuildingValue(location.getBuildingValueOriginalCcy() * exchangeRate);
            location.setContentsValue(location.getContentsValueOriginalCcy() * exchangeRate);
            location.setTiv(location.getBiValue12Months() + location.getBuildingValue() + location.getContentsValue());
        });
    }

}
