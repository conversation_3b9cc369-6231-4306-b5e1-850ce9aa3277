package com.concirrus.submissionAdapter.service.politicalViolence;

import com.concirrus.submissionAdapter.dal.AccountRepository;
import com.concirrus.submissionAdapter.dal.LocationRepository;
import com.concirrus.submissionAdapter.dto.currencyExchange.LocationUpdateResult;
import com.concirrus.submissionAdapter.dto.enums.State;
import com.concirrus.submissionAdapter.dto.enums.UpdateType;
import com.concirrus.submissionAdapter.dto.submission.InsuredAssetsResponse;
import com.concirrus.submissionAdapter.dto.submission.Quote;
import com.concirrus.submissionAdapter.dto.submission.Submission;
import com.concirrus.submissionAdapter.model.Account;
import com.concirrus.submissionAdapter.model.Location;
import com.concirrus.submissionAdapter.dto.ValueGroup;
import com.concirrus.submissionAdapter.sal.SubmissionHandlerSal;
import com.concirrus.submissionAdapter.utils.CurrencyConverter;
import com.concirrus.submissionAdapter.utils.LocationParser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.concirrus.submissionAdapter.constants.Constants.POLITICAL_VIOLENCE;
import static com.concirrus.submissionAdapter.constants.Constants.USD;

@Component
@Slf4j
@RequiredArgsConstructor
public class PoliticalViolenceUtils {
    private final CurrencyConverter currencyConverter;
    private final SubmissionHandlerSal submissionHandlerSal;
    private final AccountRepository accountRepository;
    private final LocationRepository locationRepository;



    public int ingestLocations(String submissionId, String clientId, State state, UpdateType updateType) {
        try {
            boolean hasMore = true;
            int page = 0;
            int pageSize = 50;
            int totalLocations = 0;
            while (hasMore) {
                // Response Entity wrap
                InsuredAssetsResponse response = submissionHandlerSal.getInsuredAssetsForSubmission(submissionId,
                        POLITICAL_VIOLENCE, page, pageSize, clientId);
                if (response.getStatus() != 200) {
                    log.info("Error occurred while fetching page {} of locations for submissionId {}", page,
                            submissionId);
                    throw new RuntimeException("Failed to fetch locations for submission: " + submissionId);
                }
                List<Map<String, Object>> content = response.getResult().getContent();
                log.info("retrieved {} locations", content.size());

                List<Location> locations = content.stream()
                        .map(LocationParser::parseLocation)
                        .filter(Objects::nonNull)
                        .peek(location -> {
                            location.setState(state);
                        })
                        .toList();

                convertLocationValues(locations,clientId);
                locationRepository.saveAll(locations);
                totalLocations += locations.size();
                hasMore = !response.getResult().isLast();
                page++;
            }

            return totalLocations;
        } catch (Exception e) {
            log.error("Error occurred while ingesting locations for submissionId {} ", submissionId, e);
            throw new RuntimeException("Failed to ingest locations for submission: " + submissionId, e);
        }
    }

    public Account ingestQuote(Submission submission, Quote quote) {
        String currency = Quote.getPolicyCurrency(quote);
        Optional<Account> optional = accountRepository.findBySubmissionIdAndQuoteId(submission.getSubmissionId(), quote.getQuoteId());
        Account account;
        account = optional.orElseGet(Account::new);
        Double exchangeRate = currencyConverter.getExchangeRate(currency,submission.getClientId());
        exchangeRate = exchangeRate == null ? 1.0 : exchangeRate;
        account.setAccountName(Submission.getInsuredName(submission));
        account.setBinder(Submission.getCoverType(submission));
        account.setQuoteId(quote.getQuoteId());
        account.setExcess(exchangeRate * Quote.getExcess(quote));
        account.setExcessOriginalCcy(Quote.getExcess(quote));
        account.setExpiryDate(Quote.getExpiryDate(quote));
        account.setInceptionDate(Quote.getInceptionDate(quote));
        account.setLimit(exchangeRate * Quote.getLimit(quote));
        account.setLimitOriginalCcy(Quote.getLimit(quote));
        State state = submission.getState();

        account.setWrittenLine(Quote.getWrittenLine(quote));
        account.setLine(Quote.getSignedLine(quote));
        account.setPolicyReference(Quote.getPolicyReference(quote));
        account.setPolicyCurrency(currency);
        account.setPerils(Quote.getPeril(quote));
        account.setPremium(Quote.getPremiumFromReferencing(quote));
        account.setDeductible(exchangeRate * Quote.getPdDeductible(quote));
        account.setDeductibleOriginalCcy(Quote.getPdDeductible(quote));
        account.setSubmissionId(submission.getSubmissionId());
        account.setLineOfBusiness(submission.getLineOfBusiness());
        account.setState(state);
        accountRepository.save(account);
        return account;
    }


    public boolean haveRelevantFieldsChangedForQuote(Account account, Quote quote, Submission submission) {
        String currency = Quote.getPolicyCurrency(quote);
        Double exchangeRate = currencyConverter.getExchangeRate(currency,submission.getClientId());
        exchangeRate = exchangeRate == null ? 1.0 : exchangeRate;
        Double newDeductible = exchangeRate * Quote.getPdDeductible(quote);
        Double newExcess = exchangeRate * Quote.getExcess(quote);
        Double newLimit = exchangeRate * Quote.getLimit(quote);
        State state = submission.getState();
        log.info("Deductible {}",newDeductible);
        log.info("account Deductible {}",account.getDeductible());
        Double newLine;
        if (state != null && state != State.BOUND) {
            newLine = Quote.getWrittenLine(quote);
        } else {
            newLine = Quote.getSignedLine(quote);
        }
        boolean changed = !Objects.equals(account.getDeductible(), newDeductible) ||
                !Objects.equals(account.getExcess(), newExcess) ||
                !Objects.equals(account.getLimit(), newLimit) ||
                !Objects.equals(account.getWrittenLine(), newLine) ||
                !Objects.equals(account.getPolicyCurrency(), currency);
        log.info("Changed {}", changed);
        return changed;
    }

    public LocationUpdateResult processAllLocationPages(String submissionId, String clientId) {
        LocationUpdateResult result = new LocationUpdateResult();
        int page = 0;
        boolean hasMore = true;

        while (hasMore) {
            log.info("Processing page {} for submission {}", page, submissionId);

            InsuredAssetsResponse response = fetchInsuredAssetsPage(submissionId, clientId, page);

            if (isEmptyResponse(response)) {
                hasMore = false;
                continue;
            }

            List<Map<String, Object>> content = response.getResult().getContent();
            processLocationPage(content, submissionId, result, clientId);

            hasMore = !response.getResult().isLast();
            page++;
        }

        return result;
    }

    public void updateAccountMonetaryValues(List<Account> accounts, Map<String, Double> exchangeRates) {
        accounts.parallelStream().forEach(account -> {
            double exchangeRate = exchangeRates.get(account.getPolicyCurrency())!= null ? exchangeRates.get(account.getPolicyCurrency()) : 1.0;
            account.setExcess(account.getExcessOriginalCcy() * exchangeRate);
            account.setLimit(account.getLimitOriginalCcy() * exchangeRate);
            account.setDeductible(account.getDeductibleOriginalCcy() * exchangeRate);

        });
        accountRepository.saveAll(accounts);
    }
    public void updateLocationMonetaryValues(List<Location> locations, String clientId) {
        convertLocationValues(locations, clientId);
        locationRepository.saveAll(locations);
    }

    private InsuredAssetsResponse fetchInsuredAssetsPage(String submissionId, String clientId, int page) {
        final int PAGE_SIZE = 100;
        return submissionHandlerSal.getInsuredAssetsForSubmission(
                submissionId, POLITICAL_VIOLENCE, page, PAGE_SIZE, clientId
        );
    }

    private boolean isEmptyResponse(InsuredAssetsResponse response) {
        return response == null ||
                response.getResult() == null ||
                response.getResult().getContent() == null ||
                response.getResult().getContent().isEmpty();
    }

    private void processLocationPage(List<Map<String, Object>> content, String submissionId, LocationUpdateResult result, String clientId) {
        List<String> apiLocationIds = extractLocationIds(content);
        Map<String, Location> dbLocationsMap = getExistingLocations(apiLocationIds);
        List<Location> locationsToUpdate = new ArrayList<>();

        for (Map<String, Object> locationData : content) {
            Location apiLocation = LocationParser.parseLocation(locationData);
            if (apiLocation == null) {
                log.warn("Failed to parse location data for submission {}", submissionId);
                continue;
            }

            apiLocation.setSubmissionId(submissionId);
            locationsToUpdate.add(apiLocation);

            Location dbLocation = dbLocationsMap.get(apiLocation.getId());

            if (dbLocation == null) {
                log.info("New location {} added to submission {}", apiLocation.getId(), submissionId);
                result.addCoordinateUpdate(apiLocation.getId());
            } else {
                processExistingLocation(dbLocation, apiLocation, submissionId, result);
            }
        }

        if (!locationsToUpdate.isEmpty()) {
            convertLocationValues(locationsToUpdate, clientId);
            locationRepository.saveAll(locationsToUpdate);
        }
    }

    private List<String> extractLocationIds(List<Map<String, Object>> content) {
        return content.stream()
                .map(locationData -> (String) locationData.get("id"))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private Map<String, Location> getExistingLocations(List<String> apiLocationIds) {
        if (apiLocationIds.isEmpty()) {
            return Collections.emptyMap();
        }

        return locationRepository
                .findAllById(apiLocationIds)
                .stream()
                .collect(Collectors.toMap(Location::getId, Function.identity()));
    }

    private void processExistingLocation(Location dbLocation, Location apiLocation, String submissionId, LocationUpdateResult result) {
        boolean sandTChanged = hasSandTValuesChanged(dbLocation, apiLocation);
        boolean coordinatesChanged = hasCoordinatesChanged(dbLocation, apiLocation);

        if (sandTChanged || coordinatesChanged) {
            if (coordinatesChanged) {
                result.addCoordinateUpdate(apiLocation.getId());
            } else {
                result.addDataUpdate(apiLocation.getId());
            }
            log.info("Location {} marked for update in submission {}", apiLocation.getId(), submissionId);
        }
    }

    /**
     * Checks if coordinates (latitude/longitude) have changed between database and API location
     */
    private boolean hasCoordinatesChanged(Location dbLocation, Location apiLocation) {
        return !Objects.equals(dbLocation.getLatitude(), apiLocation.getLatitude()) ||
                !Objects.equals(dbLocation.getLongitude(), apiLocation.getLongitude());
    }

    /**
     * Checks if S&T values have changed between database and API location
     */
    private boolean hasSandTValuesChanged(Location dbLocation, Location apiLocation) {
        Map<String, ValueGroup> dbMap = dbLocation.getValueGroupMap();
        Map<String, ValueGroup> apiMap = apiLocation.getValueGroupMap();
        ValueGroup dbSandT = dbMap != null ? dbMap.get("S&T") : null;
        ValueGroup apiSandT = apiMap != null ? apiMap.get("S&T") : null;
        if (dbSandT == null && apiSandT == null) {
            return false;
        }
        if (dbSandT == null || apiSandT == null) {
            return true;
        }
        return Double.compare(dbSandT.getBiValue(), apiSandT.getBiValue()) != 0 ||
                Double.compare(dbSandT.getBuildingValue(), apiSandT.getBuildingValue()) != 0 ||
                Double.compare(dbSandT.getContentsValue(), apiSandT.getContentsValue()) != 0;
    }


    private void convertLocationValues(List<Location> locations,String clientId) {
        List<String> currencies = locations.stream()
                .map(Location::getLocationCurrency)
                .distinct()
                .filter(Objects::nonNull)
                .filter(currency -> !USD.equalsIgnoreCase(currency))
                .toList();
        Map<String,Double> exchangeRates = currencyConverter.getExchangeRates(currencies,clientId);
        locations.forEach(location -> {
            String currency = location.getLocationCurrency();
            if(currency == null){
                currency = USD;
            }
            exchangeRates.putIfAbsent(currency, 1.0);
            Double exchangeRate = USD.equalsIgnoreCase(currency)? 1.0 : exchangeRates.get(location.getLocationCurrency());

            location.setValueGroupMap(convertValueGroups(location.getOriginalCurrencyValueGroupMap(), exchangeRate));
            location.setSrccTiv(location.getSrccTiv() * exchangeRate);
            location.setWarTiv(location.getWarTiv() * exchangeRate);
            location.setNcbrTiv(location.getNcbrTiv() * exchangeRate);
            location.setAaTiv(location.getAaTiv() * exchangeRate);
            location.setT3Tiv(location.getT3Tiv() * exchangeRate);
            location.setCyberTiv(location.getCyberTiv() * exchangeRate);
            location.setSandtTiv(location.getSandtTiv() * exchangeRate);
        });
    }

    private Map<String, ValueGroup> convertValueGroups(Map<String, ValueGroup> originalValueGroups, Double exchangeRate) {
        return originalValueGroups.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> convertValueGroup(entry.getValue(), exchangeRate)));
    }

    private ValueGroup convertValueGroup(ValueGroup valueGroup, Double exchangeRate) {
        if (valueGroup == null) {
            return new ValueGroup();
        }
        if (exchangeRate == null) {
            exchangeRate = 0.0;
        }
        return new ValueGroup(
                valueGroup.getBiValue() * exchangeRate,
                valueGroup.getBuildingValue() * exchangeRate,
                valueGroup.getContentsValue() * exchangeRate
        );

    }
}
