package com.concirrus.submissionAdapter.service.politicalViolence;


import com.concirrus.submissionAdapter.config.TenantContextHolder;
import com.concirrus.submissionAdapter.dal.AggregationJobRepository;
import com.concirrus.submissionAdapter.dal.LocationTaskRepository;
import com.concirrus.submissionAdapter.dto.enums.ProcessingStatus;
import com.concirrus.submissionAdapter.model.AggregationJob;
import com.concirrus.submissionAdapter.utils.SlackNotifier;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class CleanUpService {

    private final SlackNotifier slackNotifier;

    private final AggregationJobRepository aggregationJobRepository;
    private final LocationTaskRepository locationTaskRepository;
    @Value("${cleanup.threshold:1}")
    private Long cleanUpThreshold;

    public void cleanUp(){
        try {
            String tenantAlias = TenantContextHolder.getTenantAlias();
            slackNotifier.sendAlert("Starting clean up job for tenant " + tenantAlias);
            cleanUpOldJobs();
            slackNotifier.sendAlert("Clean up job completed for tenant " + tenantAlias);
        }catch (Exception e){
            log.error("Error occurred while cleaning up old jobs: {}", e.getMessage(), e);
            slackNotifier.sendAlert("Error occurred while cleaning up old jobs: " + e.getMessage());
            throw new RuntimeException("Failed to clean up old jobs", e);
        }

    }

    private void cleanUpOldJobs(){
        List<AggregationJob> oldJobs = aggregationJobRepository.findByStatusAndUpdatedAtBefore(ProcessingStatus.COMPLETED, Instant.now().minus(cleanUpThreshold, ChronoUnit.DAYS));
        log.info("Found {} old jobs to clean up", oldJobs.size());
        if (oldJobs.isEmpty()) {
            log.info("No old jobs found to clean up");
            return;
        }
        oldJobs.forEach(job -> {
            log.info("Deleting job {} and its tasks", job.getJobId());
            locationTaskRepository.deleteByJobId(job.getJobId());
        });
        aggregationJobRepository.deleteAll(oldJobs);
    }
}
