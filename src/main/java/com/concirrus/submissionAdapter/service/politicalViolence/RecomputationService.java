package com.concirrus.submissionAdapter.service.politicalViolence;

import com.concirrus.submissionAdapter.config.TenantContextHolder;
import com.concirrus.submissionAdapter.dal.AccountDal;
import com.concirrus.submissionAdapter.dal.AccountRepository;
import com.concirrus.submissionAdapter.dal.LocationRepository;
import com.concirrus.submissionAdapter.model.Account;
import com.concirrus.submissionAdapter.model.Location;
import com.concirrus.submissionAdapter.sal.BlastZoneProcessorSal;
import com.concirrus.submissionAdapter.utils.CurrencyConverter;
import com.concirrus.submissionAdapter.utils.SlackNotifier;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
@RequiredArgsConstructor
public class RecomputationService {
    private final AccountRepository accountRepository;
    private final AccountDal accountDal;
    private final CurrencyConverter currencyConverter;
    private final LocationRepository locationRepository;
    private final PoliticalViolenceUtils politicalViolenceUtils;
    private final SlackNotifier slackNotifier;
    private final BlastZoneProcessorSal blastZoneProcessorSal;

    private static final int BATCH_SIZE = 500;
    private static final long BLAST_ZONE_DELAY_MS = 1000;

    @Async
    public void recomputeAll() {
        String tenantAlias = TenantContextHolder.getTenantAlias();
        log.info("Starting full recomputation process for tenant {}", tenantAlias);

        RecomputationResult accountResult = executeRecomputation("accounts", this::recomputeAccounts);
        RecomputationResult locationResult = executeRecomputation("locations", this::recomputeLocations);
        RecomputationResult blastZoneResult = executeRecomputation("blast zones", this::recomputeBlastZones);

        logAndNotifyResults(accountResult, locationResult, blastZoneResult);
        log.info("Full recomputation process completed for tenant {}", tenantAlias);
    }

    public RecomputationResult recomputeAccounts() {
        log.info("Starting account recomputation");

        try {
            int totalAccounts = 0;
            int page = 0;

            while (true) {
                Page<Account> accountPage = accountRepository.findAll(PageRequest.of(page, BATCH_SIZE));

                if (accountPage.isEmpty()) {
                    break;
                }

                List<Account> accounts = accountPage.getContent();
                processAccountBatch(accounts);

                totalAccounts += accounts.size();
                page++;

                log.debug("Processed batch {} with {} accounts", page, accounts.size());
            }

            log.info("Successfully recomputed {} accounts", totalAccounts);
            return RecomputationResult.success("accounts", totalAccounts);

        } catch (Exception e) {
            log.error("Failed to recompute accounts", e);
            return RecomputationResult.failure("accounts", e.getMessage());
        }
    }

    public RecomputationResult recomputeLocations() {
        log.info("Starting location recomputation");

        try {
            int totalLocations = 0;
            int page = 0;

            while (true) {
                Page<Location> locationPage = locationRepository.findAll(PageRequest.of(page, BATCH_SIZE));

                if (locationPage.isEmpty()) {
                    break;
                }

                List<Location> locations = locationPage.getContent();
                politicalViolenceUtils.updateLocationMonetaryValues(locations,TenantContextHolder.getTenantId());

                totalLocations += locations.size();
                page++;

                log.debug("Processed batch {} with {} locations", page, locations.size());
            }

            log.info("Successfully recomputed {} locations", totalLocations);
            return RecomputationResult.success("locations", totalLocations);

        } catch (Exception e) {
            log.error("Failed to recompute locations", e);
            return RecomputationResult.failure("locations", e.getMessage());
        }
    }

    public RecomputationResult recomputeBlastZones() {
        log.info("Starting blast zone recomputation");

        try {
            List<String> submissionIds = accountDal.findDistinctSubmissionIds();
            log.info("Found {} submission IDs for blast zone recomputation", submissionIds.size());

            clearSubmissionData(submissionIds);
            blastZoneProcessorSal.recomputeBlastZoneAttributes();

            log.info("Successfully recomputed blast zones for {} submissions", submissionIds.size());
            return RecomputationResult.success("blast zones", submissionIds.size());

        } catch (InterruptedException e) {
            log.error("Interrupted while recomputing blast zones", e);
            Thread.currentThread().interrupt();
            return RecomputationResult.failure("blast zones", "Interrupted while recomputing blast zones");
        }
        catch (Exception e) {
            log.error("Failed to recompute blast zones", e);
            return RecomputationResult.failure("blast zones", e.getMessage());
        }
    }

    private void processAccountBatch(List<Account> accounts) {
        List<String> policyCurrencies = accounts.stream()
                .map(Account::getPolicyCurrency)
                .distinct()
                .toList();

        Map<String, Double> exchangeRates = currencyConverter.getExchangeRates(policyCurrencies, TenantContextHolder.getTenantId());
        politicalViolenceUtils.updateAccountMonetaryValues(accounts, exchangeRates);
    }

    private void clearSubmissionData(List<String> submissionIds) throws InterruptedException {
        for (String submissionId : submissionIds) {
            blastZoneProcessorSal.clearSubmissionData(submissionId);
            TimeUnit.MILLISECONDS.sleep(BLAST_ZONE_DELAY_MS);
        }
    }

    private RecomputationResult executeRecomputation(String type, java.util.function.Supplier<RecomputationResult> operation) {
        try {
            return operation.get();
        } catch (Exception e) {
            log.error("Unexpected error during {} recomputation", type, e);
            return RecomputationResult.failure(type, e.getMessage());
        }
    }

    private void logAndNotifyResults(RecomputationResult... results) {
        for (RecomputationResult result : results) {
            if (!result.isSuccess()) {
                String message = String.format("Failed to recompute %s: %s", result.getType(), result.getErrorMessage());
                log.error(message);
                slackNotifier.sendAlert(message);
            }
        }
    }

    @Getter
    public static class RecomputationResult {
        private final boolean success;
        private final String type;
        private final int processedCount;
        private final String errorMessage;

        private RecomputationResult(boolean success, String type, int processedCount, String errorMessage) {
            this.success = success;
            this.type = type;
            this.processedCount = processedCount;
            this.errorMessage = errorMessage;
        }

        public static RecomputationResult success(String type, int processedCount) {
            return new RecomputationResult(true, type, processedCount, null);
        }

        public static RecomputationResult failure(String type, String errorMessage) {
            return new RecomputationResult(false, type, 0, errorMessage);
        }

    }
}