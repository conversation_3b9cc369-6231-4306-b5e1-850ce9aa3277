package com.concirrus.submissionAdapter.model;


import com.concirrus.submissionAdapter.dto.ValueGroup;
import com.concirrus.submissionAdapter.dto.enums.State;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.geo.GeoJsonPoint;
import org.springframework.data.mongodb.core.index.GeoSpatialIndexType;
import org.springframework.data.mongodb.core.index.GeoSpatialIndexed;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Document(collection = "locations")
public class Location {

    @Id
    private String id; 
    private String locationName;
    private String street;
    private String city;
    private String postCode;
    private Double latitude;
    private Double longitude;
    private String geocodingGrade;
    private Double geocodingResolution;
    private String locationCurrency;
    private State state;


    @GeoSpatialIndexed(type = GeoSpatialIndexType.GEO_2DSPHERE)
    private GeoJsonPoint geometry;
    @Indexed
    private String submissionId;

    private Map<String, ValueGroup> originalCurrencyValueGroupMap;

    private  Map<String,ValueGroup> valueGroupMap;

    private Double srccTiv;
    private Double warTiv;
    private Double ncbrTiv;
    private Double aaTiv;
    private Double t3Tiv;
    private Double cyberTiv;
    private Double sandtTiv;
     // {also need to store based on peril}
}
