package com.concirrus.submissionAdapter.model;

import com.concirrus.submissionAdapter.dto.enums.ProcessingStatus;
import com.concirrus.submissionAdapter.dto.enums.UpdateType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.Instant;
import java.util.List;

/**
 * Model for tracking BlastZone processing jobs
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Document(collection = "aggregation_jobs")
public class AggregationJob {

    @Id
    private String jobId;
    private String submissionId;
    private String clientId;
    private String quoteId;
    private UpdateType updateType;
    private int countOfLocations;
    private ProcessingStatus status;

    @Builder.Default
    @Indexed(name = "createdOn_ttl_idx", expireAfter = "30d")
    private Instant createdAt = Instant.now();

    private Instant updatedAt;
    private String comment;

    private List<String> miAnalysisJobIds;
}
