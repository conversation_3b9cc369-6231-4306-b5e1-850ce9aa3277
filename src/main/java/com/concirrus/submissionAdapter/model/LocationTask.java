package com.concirrus.submissionAdapter.model;

import com.concirrus.submissionAdapter.dto.enums.ProcessingStatus;
import com.concirrus.submissionAdapter.dto.enums.UpdateType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.Instant;

/**
 * Model for tracking individual BlastZone processing tasks (locations)
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Document(collection = "location_tasks")
public class LocationTask {

    @Id
    private String taskId;

    private String jobId;
    private String locationId;
    private ProcessingStatus status;
    private UpdateType updateType;
    @Builder.Default
    private Instant createdOn = Instant.now();

    private Instant updatedOn;
}
