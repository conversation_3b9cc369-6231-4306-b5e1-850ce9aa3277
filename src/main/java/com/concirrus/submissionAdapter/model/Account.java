package com.concirrus.submissionAdapter.model;

import com.concirrus.submissionAdapter.dto.enums.State;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

import static com.concirrus.submissionAdapter.constants.Constants.POLITICAL_VIOLENCE;
import static com.concirrus.submissionAdapter.dto.enums.State.BOUND;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "account")
public class Account {
    @Id
    private String id;
    @Indexed
    private String quoteId;
    private String accountName;
    @Indexed
    private String submissionId;
    private String policyReference;
    private String binder;
    private String inceptionDate;
    private String expiryDate;
    private Double tiv;
    private Double limit;
    private Double limitOriginalCcy;
    private Double excess;
    private Double excessOriginalCcy;
    private Double deductible;
    private Double deductibleOriginalCcy;
    private Double line;
    private Double writtenLine;
    private Double exposure;
    private Double pml;
    private Double premium;
    private String policyCurrency;
    private List<String> perils;
    private State state;
    private String lineOfBusiness;


    public boolean isValidAccount(){
        return POLITICAL_VIOLENCE.equalsIgnoreCase(lineOfBusiness) &&perils != null && perils.contains("S&T") && !BOUND.equals(state);
    }
}
