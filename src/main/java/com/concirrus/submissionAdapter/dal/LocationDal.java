package com.concirrus.submissionAdapter.dal;


import com.concirrus.submissionAdapter.dto.enums.State;
import com.concirrus.submissionAdapter.model.Location;
import lombok.RequiredArgsConstructor;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
public class LocationDal {

    private final MongoTemplate mongoTemplate;


    public void updateLocationState(String submissionId, State state) {
        Query query = new Query();
        query.addCriteria(Criteria.where("submissionId").is(submissionId));
        Update update = new Update().set("state", state);
        mongoTemplate.updateMulti(query, update, Location.class);
    }

    public void deleteBySubmissionId(String submissionId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("submissionId").is(submissionId));
        mongoTemplate.remove(query, Location.class);
    }
}
