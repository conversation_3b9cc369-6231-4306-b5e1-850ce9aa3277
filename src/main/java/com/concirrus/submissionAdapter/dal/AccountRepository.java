package com.concirrus.submissionAdapter.dal;

import com.concirrus.submissionAdapter.model.Account;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface AccountRepository extends MongoRepository<Account, String> {

    boolean existsBySubmissionId(String submissionId);

    List<Account> findBySubmissionId(String submissionId);

    Optional<Account> findBySubmissionIdAndQuoteId(String submissionId, String quoteId);

    Optional<Account> findOneBySubmissionId(String submissionId);

    void deleteBySubmissionId(String submissionId);
}
