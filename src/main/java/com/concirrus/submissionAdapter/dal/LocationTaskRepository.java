package com.concirrus.submissionAdapter.dal;

import com.concirrus.submissionAdapter.dto.enums.ProcessingStatus;
import com.concirrus.submissionAdapter.model.LocationTask;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface LocationTaskRepository extends MongoRepository<LocationTask, String> {

    Optional<LocationTask> findByJobIdAndLocationId(String jobId, String locationId);

    List<LocationTask> findByJobId(String jobId);

    List<LocationTask> findByJobIdAndStatus(String jobId, ProcessingStatus status);

    long countByJobIdAndStatus(String jobId, ProcessingStatus status);
    void deleteByJobId(String jobId);
}
