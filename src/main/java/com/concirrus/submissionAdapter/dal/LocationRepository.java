package com.concirrus.submissionAdapter.dal;

import com.concirrus.submissionAdapter.model.Location;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface LocationRepository extends MongoRepository<Location, String> {

    List<Location> findBySubmissionId(String submissionId);

    Location findByIdAndSubmissionId(String id, String submissionId);

    Page<Location> findBySubmissionId(String submissionId, Pageable pageable);

    Page<Location> findByIdIn(List<String> ids, Pageable pageable);

    boolean existsBySubmissionId( String submissionId);
    void deleteBySubmissionId(String submissionId);

    long countBySubmissionId(String submissionId);
}
