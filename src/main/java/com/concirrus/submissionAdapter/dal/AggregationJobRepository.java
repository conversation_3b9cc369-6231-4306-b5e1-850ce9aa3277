package com.concirrus.submissionAdapter.dal;

import com.concirrus.submissionAdapter.dto.enums.ProcessingStatus;
import com.concirrus.submissionAdapter.model.AggregationJob;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Repository
public interface AggregationJobRepository extends MongoRepository<AggregationJob, String> {

    Optional<AggregationJob> findByJobId(String jobId);

    List<AggregationJob> findByStatusAndUpdatedAtBefore(ProcessingStatus status, Instant threshold);
}
