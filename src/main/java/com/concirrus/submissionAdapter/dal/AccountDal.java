package com.concirrus.submissionAdapter.dal;



import com.concirrus.submissionAdapter.model.Account;
import lombok.RequiredArgsConstructor;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@RequiredArgsConstructor
public class AccountDal {
    private final MongoTemplate mongoTemplate;

    public Account createOrUpdateAccount(Account account) {
        Query query = new Query(Criteria.where("submissionId").is(account.getSubmissionId())
                .and("quoteId").is(account.getQuoteId()));

        Update update = new Update()
                .set("accountName", account.getAccountName())
                .set("policyReference", account.getPolicyReference())
                .set("binder", account.getBinder())
                .set("inceptionDate", account.getInceptionDate())
                .set("expiryDate", account.getExpiryDate())
                .set("tiv", account.getTiv())
                .set("limit", account.getLimit())
                .set("limitOriginalCcy", account.getLimitOriginalCcy())
                .set("excess", account.getExcess())
                .set("excessOriginalCcy", account.getExcessOriginalCcy())
                .set("deductible", account.getDeductible())
                .set("deductibleOriginalCcy", account.getDeductibleOriginalCcy())
                .set("line", account.getLine())
                .set("writtenLine", account.getWrittenLine())
                .set("exposure", account.getExposure())
                .set("pml", account.getPml())
                .set("premium", account.getPremium())
                .set("policyCurrency", account.getPolicyCurrency())
                .set("perils", account.getPerils())
                .set("state", account.getState())
                .set("lineOfBusiness", account.getLineOfBusiness())
                // Add submissionId and quoteId in case of insert
                .setOnInsert("submissionId", account.getSubmissionId())
                .setOnInsert("quoteId", account.getQuoteId());

        FindAndModifyOptions options = FindAndModifyOptions.options()
                .returnNew(true)
                .upsert(true);


        return mongoTemplate.findAndModify(query, update, options, Account.class);
    }

    public List<String> findDistinctSubmissionIds() {
        return mongoTemplate.query(Account.class)
                .distinct("submissionId")
                .as(String.class)
                .all();
    }
}
