package com.concirrus.submissionAdapter.dal;



import com.concirrus.submissionAdapter.model.Account;
import lombok.RequiredArgsConstructor;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@RequiredArgsConstructor
public class AccountDal {
    private final MongoTemplate mongoTemplate;

    public void deleteAccountBySubmissionId(String submissionId){
        Query query = new Query();
        query.addCriteria(Criteria.where("submissionId").is(submissionId));
        mongoTemplate.remove(query, Account.class);
    }

    public void deleteBySubmissionIdAndQuoteId(String submissionId, String quoteId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("submissionId").is(submissionId).and("quoteId").is(quoteId));
        mongoTemplate.remove(query, Account.class);
    }

    public List<String> findDistinctSubmissionIds() {
        return mongoTemplate.query(Account.class)
                .distinct("submissionId")
                .as(String.class)
                .all();
    }
}
