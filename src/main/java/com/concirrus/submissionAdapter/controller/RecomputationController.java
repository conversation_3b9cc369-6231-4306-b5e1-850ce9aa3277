package com.concirrus.submissionAdapter.controller;


import com.concirrus.submissionAdapter.config.TenantContextHolder;
import com.concirrus.submissionAdapter.service.TenantService;
import com.concirrus.submissionAdapter.service.politicalViolence.RecomputationService;
import com.concirrus.submissionAdapter.utils.SlackNotifier;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.concirrus.submissionAdapter.constants.Constants.CLIENT_ID;

@RestController
@RequestMapping("recompute")
@RequiredArgsConstructor
@Slf4j
public class RecomputationController {

    private final RecomputationService recomputationService;
    private final TenantService tenantService;
    @RequestMapping("/all")
    public void recomputeAll(@RequestHeader(value = CLIENT_ID, required = true) String clientId){
        String tenantAlias = tenantService.getTenantAlias(clientId);
        TenantContextHolder.setTenantContext(clientId, tenantAlias);
        recomputationService.recomputeAll();
    }

}
