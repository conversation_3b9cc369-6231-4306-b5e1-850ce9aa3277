package com.concirrus.submissionAdapter.controller;


import com.concirrus.submissionAdapter.config.TenantContextHolder;
import com.concirrus.submissionAdapter.service.TenantService;
import com.concirrus.submissionAdapter.service.politicalViolence.CleanUpService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.coyote.BadRequestException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;

import static com.concirrus.submissionAdapter.constants.Constants.CLIENT_ID;

@RestController
@RequestMapping("cleanup")
@RequiredArgsConstructor
@Slf4j
public class CleanUpController {

    private final CleanUpService cleanUpService;
    private final TenantService tenantService;
    @DeleteMapping("/old-jobs")
    public void cleanUp(@RequestHeader(value = CLIENT_ID) String clientId) throws BadRequestException {
        log.info("Starting clean up job for client {}", clientId);
        if (clientId == null || clientId.isBlank()) {
            throw new BadRequestException("Client ID cannot be null or empty");
        }
        String tenantAlias = tenantService.getTenantAlias(clientId);
        TenantContextHolder.setTenantContext(clientId, tenantAlias);
        cleanUpService.cleanUp();

    }
    @ExceptionHandler(BadRequestException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public void handleBadRequest(Exception e) {
        log.warn("Bad request: {}", e.getMessage());
    }
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public void handleException(Exception e) {
        log.error("Exception during cleanup", e);
    }

}
