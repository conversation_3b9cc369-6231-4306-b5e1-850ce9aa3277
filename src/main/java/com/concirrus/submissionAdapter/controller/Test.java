package com.concirrus.submissionAdapter.controller;


import com.concirrus.submissionAdapter.config.TenantContextHolder;
import com.concirrus.submissionAdapter.dto.enums.ProductType;
import com.concirrus.submissionAdapter.dto.workflow.SubmissionUpdateEvent;
import com.concirrus.submissionAdapter.sal.QuoteServiceSal;
import com.concirrus.submissionAdapter.sal.SubmissionHandlerSal;
import com.concirrus.submissionAdapter.service.FactoryMap;
import com.concirrus.submissionAdapter.service.MessageHandlerFactory;
import com.concirrus.submissionAdapter.service.TenantService;
import com.concirrus.submissionAdapter.utils.CurrencyConverter;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("test")
public class Test {

    private final SubmissionHandlerSal submissionHandlerSal;
    private final QuoteServiceSal quoteServiceSal;
    private final FactoryMap factoryMap;
    private final CurrencyConverter currencyConverter;
    private final TenantService tenantService;

    public Test(SubmissionHandlerSal submissionHandlerSal, QuoteServiceSal quoteServiceSal, FactoryMap factoryMap, CurrencyConverter currencyConverter, TenantService tenantService) {
        this.submissionHandlerSal = submissionHandlerSal;
        this.quoteServiceSal = quoteServiceSal;
        this.factoryMap = factoryMap;
        this.currencyConverter = currencyConverter;
        this.tenantService = tenantService;
    }


    @GetMapping("/one")
    public Object test1(@RequestParam(value = "submissionId")String submissionId,@RequestParam(value = "client") String clientId){
        String tenantAlias = tenantService.getTenantAlias(clientId);
        TenantContextHolder.setTenantContext(clientId, tenantAlias);
        try {
            return submissionHandlerSal.getSubmissionById(submissionId,clientId);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        finally {
            TenantContextHolder.clear();
        }
    }

    @GetMapping("/two")
    public Object test2(@RequestParam(value = "submissionId")String submissionId,@RequestParam(value = "client") String clientId){
        String tenantAlias = tenantService.getTenantAlias(clientId);
        TenantContextHolder.setTenantContext(clientId, tenantAlias);
        try {
            return submissionHandlerSal.getInsuredAssetsForSubmission(submissionId, "POLITICAL_VIOLENCE", 0, 20, clientId);
        }catch (Exception e){
            throw new RuntimeException(e);
        }finally {
            TenantContextHolder.clear();
        }
    }

    @GetMapping("/three")
    public Object test3(@RequestParam(value = "quoteId") String quoteId, @RequestParam(value = "client") String clientId) {
        String tenantAlias = tenantService.getTenantAlias(clientId);
        TenantContextHolder.setTenantContext(clientId, tenantAlias);
        try {
            return quoteServiceSal.getQuoteById(quoteId, clientId);
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            TenantContextHolder.clear();
        }
    }

    @PostMapping("/ingest-submission")
    public Object ingestSubmission(@RequestBody SubmissionUpdateEvent updateEvent){
        String tenantAlias = tenantService.getTenantAlias(updateEvent.getClientId());
        TenantContextHolder.setTenantContext(updateEvent.getClientId(), tenantAlias);
        try {
            ProductType productType = ProductType.valueOf(updateEvent.getProductType());
            MessageHandlerFactory factory =  factoryMap.getFactory(productType);
            factory.processSubmissionUpdate(updateEvent);
            return "ingestionStarted";
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            TenantContextHolder.clear();
        }
    }


    @GetMapping("/currency/{currency}")
    public Object test(@PathVariable(value = "currency") String currency,@RequestHeader(value = "client-id") String clientId){
        String tenantAlias = tenantService.getTenantAlias(clientId);
        TenantContextHolder.setTenantContext(clientId, tenantAlias);
        try {
            return currencyConverter.getExchangeRate(currency,clientId);
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            TenantContextHolder.clear();
        }
    }

    @PostMapping("/currencies")
    public Object test(@RequestBody List<String> currencies,@RequestHeader(value = "client-id") String clientId){
        String tenantAlias = tenantService.getTenantAlias(clientId);
        TenantContextHolder.setTenantContext(clientId, tenantAlias);
        try {
        return currencyConverter.getExchangeRates(currencies,clientId);
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            TenantContextHolder.clear();
        }
    }

}
