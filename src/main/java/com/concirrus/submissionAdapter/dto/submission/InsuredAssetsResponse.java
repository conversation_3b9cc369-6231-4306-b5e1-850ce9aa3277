package com.concirrus.submissionAdapter.dto.submission;

import lombok.Data;

import java.time.Instant;
import java.util.List;
import java.util.Map;

@Data
public class InsuredAssetsResponse {
    private int status;
    private Instant timestamp;
    private Result result;
    private String error;

    @Data
    public static class Result {
        private List<Map<String, Object>> content;
        private Pageable pageable;
        private int totalPages;
        private int totalElements;
        private boolean last;
        private int size;
        private int number;
        private Sort sort;
        private int numberOfElements;
        private boolean first;
        private boolean empty;
    }

    @Data
    public static class Pageable {
        private Sort sort;
        private int offset;
        private int pageNumber;
        private int pageSize;
        private boolean paged;
        private boolean unpaged;
    }

    @Data
    public static class Sort {
        private boolean empty;
        private boolean unsorted;
        private boolean sorted;
    }
}
