package com.concirrus.submissionAdapter.dto.submission;


import com.concirrus.submissionAdapter.dto.enums.State;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class Submission {
    private static final String INSURED_NAME= "insuredName";
    private static final String COVER_TYPE = "coverType";

    @NotBlank(message = "Submission ID cannot be null or empty")
    private String submissionId;

    private String lineOfBusiness;

    private String coverageType;

    @NotBlank(message = "Client ID cannot be null or empty")
    private String clientId;

    private String inceptionDate;
    private String expiryDate;
    private Map<String,Object> insuredInfo;

    @Getter
    private State state;

    private static String extractFromInsuredInfo(Submission submission,String key){
        if (submission == null || key == null || key.isBlank()) {
            return null;
        }

        Map<String, Object> insuredInfo = submission.getInsuredInfo();
        Object value = (insuredInfo != null) ? insuredInfo.get(key) : null;

        return (value instanceof String) ? (String) value : null;
    }

    public static String getInsuredName(Submission submission){
        return extractFromInsuredInfo(submission,INSURED_NAME);
    }

    public static String getCoverType(Submission submission){
        return extractFromInsuredInfo(submission,COVER_TYPE);
    }

}
