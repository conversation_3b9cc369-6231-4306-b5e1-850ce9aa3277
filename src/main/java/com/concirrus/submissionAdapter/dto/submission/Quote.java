package com.concirrus.submissionAdapter.dto.submission;


import com.concirrus.submissionAdapter.dto.enums.State;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class Quote {

    private static final String INCEPTION_DATE = "inceptionDate";
    private static final String EXPIRY_DATE = "expiryDate";
    private static final String POLICY_CURRENCY = "policyCurrency";
    private static final String LIMIT = "limits";
    private static final String EXCESS = "excess";
    private static final String PERIL = "peril";
    private static final String SIGNED_LINE = "signedLine";
    private static final String WRITTEN_LINE = "writtenLine";
    private static final String APPLIED_EXPOSURE = "appliedExposure";
    private static final String POLICY_REFERENCE = "policyReference";
    private static final String TOTAL_PREMIUM_IN_DOLLARS = "totalPremiumInDollars";
    private static final String APPLIED_CRISIS_NET_PREMIUM_USD= "appliedCrisisNetPremiumUsd";
    private static final String PD_DEDUCTIBLE = "pdDeductible";


    @NotBlank(message = "Submission ID cannot be null or empty")
    private String submissionID;

    @NotBlank(message = "Client ID cannot be null or empty")
    private String clientId;

    private Map<String,Object> riskDetails;
    private Map<String,Object>  commissions;
    private Map<String,Object>  referencing;
    private Map<String,Object> financeOperation;

    @NotBlank(message = "Quote ID cannot be null or empty")
    private String quoteId;

    private String status;


    @SuppressWarnings("unchecked")
    private static <T> T extractFromMap(Map<String,Object>map, String key, Class<T> type) {
        if (map == null || key == null || key.isBlank()) {
            return null;
        }
        Object value =  map.get(key);

        if (value == null) {
            return null;
        }

        if (type.isInstance(value)) {
            return (T) value;
        }

        // Try to convert String to Double if needed
        if (type == Double.class) {
            if (value instanceof Number number) {
                return (T) Double.valueOf(number.doubleValue());
            }
            if (value instanceof String strValue) {
                try {
                    return (T) Double.valueOf(strValue);
                } catch (NumberFormatException e) {
                    return null;
                }
            }
        }

        // Try to handle List<String> when it's a List of Objects
        if (type == List.class && value instanceof List<?> list) {
            return (T) list.stream()
                    .filter(Objects::nonNull)
                    .map(Object::toString)
                    .collect(Collectors.toList());
        }

        return null;
    }

    public static String getInceptionDate(Quote quote) {
        return extractFromMap(quote.riskDetails, INCEPTION_DATE, String.class);
    }

    public static String getExpiryDate(Quote quote) {
        return extractFromMap(quote.riskDetails, EXPIRY_DATE, String.class);
    }

    public static String getPolicyCurrency(Quote quote) {
        return extractFromMap(quote.riskDetails, POLICY_CURRENCY, String.class);
    }

    public static Double getLimit(Quote quote) {
        return getDoubleOrDefault(extractFromMap(quote.riskDetails, LIMIT, Double.class), 0.0);
    }

    public static Double getExcess(Quote quote) {
        return getDoubleOrDefault(extractFromMap(quote.riskDetails, EXCESS, Double.class), 0.0);
    }
    public static Double getPdDeductible(Quote quote) {
        return getDoubleOrDefault(extractFromMap(quote.riskDetails, PD_DEDUCTIBLE, Double.class), 0.0);
    }

    @SuppressWarnings("unchecked")
    public static List<String> getPeril(Quote quote) {
        return extractFromMap(quote.riskDetails, PERIL, List.class);
    }
    
    public static Double getSignedLine(Quote quote) {
        return getDoubleOrDefault(extractFromMap(quote.getReferencing(), SIGNED_LINE, Double.class),100);
    }

    public static Double getWrittenLine(Quote quote) {
        return getDoubleOrDefault(extractFromMap(quote.getReferencing(), WRITTEN_LINE, Double.class),100);
    }

    public static Double getAppliedExposure(Quote quote) {
        return extractFromMap(quote.getReferencing(), APPLIED_EXPOSURE, Double.class);
    }

    public static String getPolicyReference(Quote quote) {
        return extractFromMap(quote.getReferencing(), POLICY_REFERENCE, String.class);
    }
    
    public static Double getPremium(Quote quote) {
        return extractFromMap(quote.getFinanceOperation(),TOTAL_PREMIUM_IN_DOLLARS,Double.class);
    }

    public static Double getPremiumFromReferencing(Quote quote) {
        return getDoubleOrDefault(extractFromMap(quote.getReferencing(),APPLIED_CRISIS_NET_PREMIUM_USD,Double.class),0.0);
    }

    private static Double getDoubleOrDefault(Double value, double defaultValue) {
        if (value == null) return defaultValue;
        try {
            return Double.parseDouble(value.toString());
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    public State getState() {
       try {
              return State.valueOf(this.status);
         } catch (Exception e) {
              // If the state is not valid, return a default state or handle accordingly
              return null; // Assuming UNKNOWN is a valid state in your enum
       }
    }

}
