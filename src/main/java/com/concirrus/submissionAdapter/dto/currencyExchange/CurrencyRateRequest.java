package com.concirrus.submissionAdapter.dto.currencyExchange;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CurrencyRateRequest {
    private String fromCcy;
    private String toCcy;
    @Builder.Default
    private String asOfDate = LocalDate.now().minusDays(1).toString() ;
}
