package com.concirrus.submissionAdapter.dto.currencyExchange;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

// Helper class to manage update results
@Data
@AllArgsConstructor
public class LocationUpdateResult {
    private  List<String> coordinateUpdatedLocations ;
    private  List<String> dataUpdatedLocations ;

    public LocationUpdateResult() {
        this.coordinateUpdatedLocations = new ArrayList<>();
        this.dataUpdatedLocations = new ArrayList<>();
    }

   public boolean hasCoordinateUpdates() {
        return !coordinateUpdatedLocations.isEmpty();
    }

    public boolean hasDataUpdates() {
        return !dataUpdatedLocations.isEmpty();
    }

    public int getTotalUpdatedCount() {
        return coordinateUpdatedLocations.size() + dataUpdatedLocations.size();
    }

    public void addCoordinateUpdate(String locationId) {
        coordinateUpdatedLocations.add(locationId);
    }

    public void addDataUpdate(String locationId) {
        dataUpdatedLocations.add(locationId);
    }
}
