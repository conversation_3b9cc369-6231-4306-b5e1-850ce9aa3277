package com.concirrus.submissionAdapter.dto.miAnalysis;

import com.concirrus.submissionAdapter.dto.enums.ProcessingStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;

import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MiAnalysisJob {
    @Id
    private String jobId;

    private ProcessingStatus status;

    private String submissionId;

    private String quoteId;

    private Instant createdAt;

    private Instant updatedAt;
}
