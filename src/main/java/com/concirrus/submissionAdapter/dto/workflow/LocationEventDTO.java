package com.concirrus.submissionAdapter.dto.workflow;

import com.concirrus.submissionAdapter.dto.enums.State;
import com.concirrus.submissionAdapter.dto.enums.UpdateType;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LocationEventDTO {
    @NotBlank(message = "Submission ID cannot be null or empty")
    private String submissionId;

    @NotBlank(message = "Location ID cannot be null or empty")
    private String locationId;

    @NotNull(message = "Longitude cannot be null")
    @DecimalMin(value = "-180.0", message = "Longitude must be between -180 and 180")
    @DecimalMax(value = "180.0", message = "Longitude must be between -180 and 180")
    private Double longitude;

    @NotNull(message = "Latitude cannot be null")
    @DecimalMin(value = "-90.0", message = "Latitude must be between -90 and 90")
    @DecimalMax(value = "90.0", message = "Latitude must be between -90 and 90")
    private Double latitude;

    @NotNull(message = "State cannot be null")
    private State state;

    @NotNull(message = "Update type cannot be null")
    private UpdateType updateType;

    @NotNull(message = "job Id cannot be null")
    private String jobId;

    @NotBlank(message = "Client ID cannot be null or empty")
    private String clientId;
}
