package com.concirrus.submissionAdapter.dto.workflow;

import com.concirrus.submissionAdapter.dto.enums.ProductType;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SubmissionUpdateEvent {
    @NotBlank(message = "Client ID cannot be null or empty")
    private String clientId;

    @NotBlank(message = "Submission ID cannot be null or empty")
    private String submissionId;

    private String quoteId;

    @NotBlank(message = "Update type cannot be null or empty")
    private String updateType;

    @NotBlank(message = "Product type cannot be null or empty")
    private String productType = ProductType.AVIATION_WAR.name();
}
