package com.concirrus.submissionAdapter.dto.workflow;

import com.concirrus.submissionAdapter.dto.enums.ProcessingStatus;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for receiving callbacks from BlastZone processor service
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskCallBack {

    private String locationId;

    @NotBlank(message = "Job ID cannot be null or empty")
    private String jobId;

    private String taskId;

    @NotBlank(message = "Status cannot be null or empty")
    private ProcessingStatus status;

    private String clientId;
}
