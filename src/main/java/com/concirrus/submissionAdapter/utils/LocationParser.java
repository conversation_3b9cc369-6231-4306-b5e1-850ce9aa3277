package com.concirrus.submissionAdapter.utils;

import com.concirrus.submissionAdapter.model.Location;
import com.concirrus.submissionAdapter.model.ValueGroup;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.geo.GeoJsonPoint;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Slf4j
@RequiredArgsConstructor
public class LocationParser {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static Location parseLocation(Map<String, Object> input) {
        if (input == null) {
            log.warn("Input map is null, cannot parse locations");
            return null;
        }

        try {
            Location location = new Location();

            Map<String, Object> locationObj = safeGetMap(input, "location");
            if (locationObj != null) {
                location.setId(safeGetString(input, "id"));
                location.setLocationName(safeGetString(locationObj, "name"));
                location.setStreet(safeGetString(locationObj, "streetName"));
                location.setCity(safeGetString(locationObj, "cityName"));
                location.setPostCode(safeGetString(locationObj, "postalCode"));
                location.setLatitude(asDouble(locationObj.get("latitude")));
                location.setLongitude(asDouble(locationObj.get("longitude")));
                location.setGeocodingGrade(getGrade(location.getGeocodingResolution()));
                location.setGeocodingResolution(asDouble(locationObj.get("geocodingResolution")));
                location.setLocationCurrency(safeGetString(locationObj, "currency"));
                location.setSubmissionId(safeGetString(input, "submissionId"));
                // Only create geometry if both latitude and longitude are valid
                if (location.getLongitude() != null && location.getLatitude() != null) {
                    location.setGeometry(new GeoJsonPoint(location.getLongitude(), location.getLatitude()));
                }
            }
            location.setOriginalCurrencyValueGroupMap(buildValueGroupMap(input));
            location.setSrccTiv(sumValues(safeGetMap(input, "srccValues")));
            location.setWarTiv(sumValues(safeGetMap(input, "warValues")));
            location.setNcbrTiv(sumValues(safeGetMap(input, "ncbrValues")));
            location.setAaTiv(sumValues(safeGetMap(input, "aaValues")));
            location.setT3Tiv(sumValues(safeGetMap(input, "t3Values")));
            location.setCyberTiv(sumValues(safeGetMap(input, "cyberValues")));
            location.setSandtTiv(sumValues(safeGetMap(input, "sAndTValues")));
            return location;
        }catch (Exception e){
            log.error("Error occurred in parsing location: {}", e.getMessage(), e);
            return null;
        }
    }

    private static   Map<String, ValueGroup> buildValueGroupMap(Map<String, Object> input){
        Map<String,ValueGroup>valueGroupMap = new HashMap<>();
        if(CollectionUtils.isEmpty(input)){
            valueGroupMap.put("S&T", new ValueGroup());
            valueGroupMap.put("SRCC", new ValueGroup());
            valueGroupMap.put("War",new ValueGroup());
            return valueGroupMap;
        }

        try {
            valueGroupMap.put("S&T", safeConvertToValueGroup(input.get("sAndTValues")));
            valueGroupMap.put("SRCC", safeConvertToValueGroup(input.get("srccValues")));
            valueGroupMap.put("War", safeConvertToValueGroup(input.get("warValues")));
        } catch (Exception e) {
            log.warn("Error converting value groups, using default empty groups: {}", e.getMessage());
            valueGroupMap.put("S&T", new ValueGroup());
            valueGroupMap.put("SRCC", new ValueGroup());
            valueGroupMap.put("War", new ValueGroup());
        }

        return valueGroupMap;
    }

    private static ValueGroup safeConvertToValueGroup(Object value) {
        if (value == null) {
            return new ValueGroup();
        }
        try {
            return objectMapper.convertValue(value, ValueGroup.class);
        } catch (Exception e) {
            log.warn("Failed to convert value to ValueGroup: {}", e.getMessage());
            return new ValueGroup();
        }
    }

    @SuppressWarnings("unchecked")
    private static Map<String, Object> safeGetMap(Map<String, Object> input, String key) {
        if (input == null || key == null) {
            return null;
        }
        Object value = input.get(key);
        if (value instanceof Map) {
            return (Map<String, Object>) value;
        }
        return null;
    }

    private static String safeGetString(Map<String, Object> input, String key) {
        if (input == null || key == null) {
            return null;
        }
        Object value = input.get(key);
        if (value == null) {
            return null;
        }
        return String.valueOf(value);
    }

    private static Double sumValues(Map<String, Object> perilValues) {
        if (perilValues == null) return 0.0;

        return asDouble(perilValues.get("buildingValue"))
                + asDouble(perilValues.get("contentsValue"))
                + asDouble(perilValues.get("biValue"));
    }

    private static Double asDouble(Object value) {
        if (value == null) return 0.0;
        if (value instanceof Number) return ((Number) value).doubleValue();
        try {
            return Double.parseDouble(value.toString());
        } catch (Exception e) {
            return 0.0;
        }
    }

    private static String getGrade(Double resolution){
        if (Objects.isNull(resolution))return "Bad";
        if (resolution>80){
            return "Good";
        } else if (resolution>=65) {
            return "Ideal";
        }else {
            return "Bad";
        }
    }
}
