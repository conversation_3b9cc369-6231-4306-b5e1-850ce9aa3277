package com.concirrus.submissionAdapter.utils;

import com.concirrus.submissionAdapter.model.Location;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.geo.GeoJsonPoint;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Slf4j
@RequiredArgsConstructor
public class LocationParser {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static Location parseLocation(Map<String, Object> input) {
        if (input == null) {
            log.warn("Input map is null, cannot parse locations");
            return null;
        }

        try {
            Location location = new Location();

            Map<String, Object> locationObj = safeGetMap(input, "location");
            if (locationObj != null) {
                location.setId(safeGetString(input, "id"));
                location.setLocationName(safeGetString(locationObj, "name"));
                location.setStreet(safeGetString(locationObj, "streetName"));
                location.setCity(safeGetString(locationObj, "cityName"));
                location.setPostCode(safeGetString(locationObj, "postalCode"));
                location.setLatitude(asDouble(locationObj.get("latitude")));
                location.setLongitude(asDouble(locationObj.get("longitude")));
                location.setGeocodingGrade(getGrade(location.getGeocodingResolution()));
                location.setGeocodingResolution(asDouble(locationObj.get("geocodingResolution")));
                location.setLocationCurrency(safeGetString(locationObj, "currency"));
                location.setSubmissionId(safeGetString(input, "submissionId"));
                // Only create geometry if both latitude and longitude are valid
                if (location.getLongitude() != null && location.getLatitude() != null) {
                    location.setGeometry(new GeoJsonPoint(location.getLongitude(), location.getLatitude()));
                }
                location.setLocationCurrency(safeGetString(locationObj, "currency"));
            }
            location.setContentsValueOriginalCcy(asDouble(input.get("contentsValue")));
            location.setBuildingValueOriginalCcy(asDouble(input.get("buildingValue")));
            location.setBiValue12MonthsOriginalCcy(asDouble(input.get("biValue12Months")));
            return location;
        }catch (Exception e){
            log.error("Error occurred in parsing location: {}", e.getMessage(), e);
            return null;
        }
    }




    @SuppressWarnings("unchecked")
    private static Map<String, Object> safeGetMap(Map<String, Object> input, String key) {
        if (input == null || key == null) {
            return null;
        }
        Object value = input.get(key);
        if (value instanceof Map) {
            return (Map<String, Object>) value;
        }
        return null;
    }

    private static String safeGetString(Map<String, Object> input, String key) {
        if (input == null || key == null) {
            return null;
        }
        Object value = input.get(key);
        if (value == null) {
            return null;
        }
        return String.valueOf(value);
    }


    private static Double asDouble(Object value) {
        if (value == null) return 0.0;
        if (value instanceof Number) return ((Number) value).doubleValue();
        try {
            return Double.parseDouble(value.toString());
        } catch (Exception e) {
            return 0.0;
        }
    }

    private static String getGrade(Double resolution){
        if (Objects.isNull(resolution))return "Bad";
        if (resolution>80){
            return "Good";
        } else if (resolution>=65) {
            return "Ideal";
        }else {
            return "Bad";
        }
    }
}
