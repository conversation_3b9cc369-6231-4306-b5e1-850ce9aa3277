package com.concirrus.submissionAdapter.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class SlackNotifier {


    private final String slackWebhookUrl;

    private final RestTemplate restTemplate;

    public SlackNotifier(@Value("${slack.webhook.url}") String slackWebhookUrl, RestTemplate restTemplate) {
        this.slackWebhookUrl = slackWebhookUrl;
        this.restTemplate = restTemplate;
    }

    public void sendAlert(String message) {
        log.info(message);
        Map<String, String> payload = new HashMap<>();
        payload.put("text", message);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<Map<String, String>> entity = new HttpEntity<>(payload, headers);

        try {
            log.info("Sending Slack alert: {}", message);
            restTemplate.postForEntity(slackWebhookUrl, entity, String.class);
        } catch (Exception e) {
            // Optional: log the error or fallback
            log.info("Failed to send Slack alert: {}", e.getMessage(), e);
        }
    }
}