package com.concirrus.submissionAdapter.utils;

import com.concirrus.submissionAdapter.dto.currencyExchange.CurrencyRateApiResponse;
import com.concirrus.submissionAdapter.dto.currencyExchange.CurrencyRateData;
import com.concirrus.submissionAdapter.dto.currencyExchange.CurrencyRateRequest;
import com.concirrus.submissionAdapter.sal.CurrencyServiceSal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.concirrus.submissionAdapter.constants.Constants.USD;


@Component
@Slf4j
@RequiredArgsConstructor
public class CurrencyConverter {

    private final CurrencyServiceSal currencyServiceSal;


    public  Double getExchangeRate( String currency,String clientId) {
        if (currency == null || currency.equalsIgnoreCase(USD)) {
            return 1.0;
        }
        try {
            log.info("Getting exchange rate for currency {}", currency);
            CurrencyRateRequest request = CurrencyRateRequest.builder().fromCcy(currency).toCcy("USD").build();
           
            CurrencyRateApiResponse response = currencyServiceSal.getCurrencyRates(List.of(request),clientId);
            log.info("Response from currency service {}", response);
            return response.getData().getFirst().getRate();
        } catch (Exception e) {
            log.info("Error converting currency: {}", e.getMessage(),e);
            return 0.0;
        }
    }

    public Map<String,Double> getExchangeRates(List<String> currencies,String clientId) {
        try {
            log.info("Getting exchange rates for currencies {}", currencies);
            if (CollectionUtils.isEmpty(currencies)) {
                return new HashMap<>();
            }
            List<CurrencyRateRequest> requests = currencies.stream()
                    .map(currency -> CurrencyRateRequest.builder().fromCcy(currency).toCcy(USD).build())
                    .toList();

            CurrencyRateApiResponse response = currencyServiceSal.getCurrencyRates(requests,clientId);
            return response.getData().stream()
                    .collect(Collectors.toMap(CurrencyRateData::getFromCcy, CurrencyRateData::getRate));
        } catch (Exception e) {
            log.error("Error converting currencies: {}", e.getMessage());
            return new HashMap<>();
        }

    }

}