package com.concirrus.submissionAdapter.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.IndexOperations;
import org.springframework.data.mongodb.core.index.IndexResolver;
import org.springframework.data.mongodb.core.index.MongoPersistentEntityIndexResolver;
import org.springframework.data.mongodb.core.mapping.MongoMappingContext;
import org.springframework.data.mongodb.core.mapping.MongoPersistentEntity;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;

@Component
@Slf4j
public class TenantIndexCreator {

    private final MongoMappingContext mappingContext;

    public TenantIndexCreator(MongoMappingContext mappingContext) {
        this.mappingContext = mappingContext;
    }

    public void ensureIndexes(MongoTemplate tenantTemplate) {
        IndexResolver resolver = new MongoPersistentEntityIndexResolver(mappingContext);

        Collection<MongoPersistentEntity<?>> collections = mappingContext.getPersistentEntities();
        log.info("Ensuring indexes for {} collections", collections.size());

        mappingContext.getPersistentEntities().forEach(entity -> {
            log.info("Ensuring indexes for collection: {}", entity.getType().getSimpleName());
            IndexOperations indexOps = tenantTemplate.indexOps(entity.getType());
            resolver.resolveIndexFor(entity.getType()).forEach(indexOps::ensureIndex);
        });
    }
}