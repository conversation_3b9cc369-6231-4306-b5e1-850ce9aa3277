package com.concirrus.submissionAdapter.sal;


import com.concirrus.submissionAdapter.config.TenantContextHolder;
import com.concirrus.submissionAdapter.dto.miAnalysis.MiAnalysisJob;
import com.concirrus.submissionAdapter.dto.miAnalysis.MiAnalysisJobRequest;
import com.concirrus.submissionAdapter.dto.submission.BasicResponse;
import com.concirrus.submissionAdapter.utils.SlackNotifier;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.*;

import java.util.ArrayList;
import java.util.List;

import static com.concirrus.submissionAdapter.constants.Constants.CLIENT_ID;

@Component
@Slf4j
@RequiredArgsConstructor
public class BlastZoneProcessorSal {
    public static final String RECOMPUTE = "/recompute";
    private final RestTemplate restTemplate;
    private final SlackNotifier slackNotifier;
    private static final String BASE_URL = "http://blast-zone-processor";
    private static final String MI_ANALYSIS_ENDPOINT = "/mi-analysis";
    private static final String BLAST_ZONE_ENDPOINT = "/api/blast-zone";
    private static final String JOBS_ENDPOINT = "/jobs";
    private static final String JOB_ENDPOINT = "/job";
    private static final long JOB_TRIGGER_DELAY_MS = 1000L;

    public List<String> createMiAnalysisJobs(List<MiAnalysisJobRequest> miAnalysisJobRequests) {
        if (miAnalysisJobRequests == null || miAnalysisJobRequests.isEmpty()) {
            log.warn("No MI analysis job requests provided");
            return new ArrayList<>();
        }

        log.info("Creating {} MI analysis jobs", miAnalysisJobRequests.size());
        List<String> jobIds = new ArrayList<>();
        for (MiAnalysisJobRequest request : miAnalysisJobRequests) {
            if (request == null) {
                log.warn("Skipping null MI analysis job request");
                continue;
            }
           String jobId = createSingleMiAnalysisJob(request);
            if (jobId != null) {
                jobIds.add(jobId);
            }
        }
        return jobIds;
    }

    public void triggerMiAnalysisJobs(List<String> jobIds) {
        if (jobIds == null || jobIds.isEmpty()) {
            log.warn("No job IDs provided for triggering");
            return;
        }

        log.info("Triggering {} MI analysis jobs", jobIds.size());

        for (String jobId : jobIds) {
            if (jobId == null || jobId.trim().isEmpty()) {
                log.warn("Skipping null or empty job ID");
                continue;
            }
            triggerSingleMiAnalysisJob(jobId.trim());
            addDelayBetweenJobs();
        }
    }

    public boolean clearSubmissionData(String submissionId) {
        try {
            String url = BASE_URL + BLAST_ZONE_ENDPOINT + "/delete-mapping?submissionId=" + submissionId;
            HttpEntity<Object> entity = createHttpEntity(null);

            ResponseEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.DELETE,
                    entity,
                    String.class
            );

            if (!response.getStatusCode().is2xxSuccessful()) {
                log.error("Failed to clear submission data for submission {}: HTTP {}",
                        submissionId, response.getStatusCode());
                return false;
            } else {
                log.info("Successfully cleared submission data for submission {}", submissionId);
                return true;
            }
        } catch (Exception ex) {
            log.error("Error while clearing submission data for submission {}: {}", submissionId, ex.getMessage());
            return false;
        }
    }

    public void recomputeBlastZoneAttributes() {
        try {
            String url = BASE_URL + BLAST_ZONE_ENDPOINT + RECOMPUTE;
            HttpEntity<Object> entity = createHttpEntity(null);

            ResponseEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    entity,
                    String.class
            );

            if (!response.getStatusCode().is2xxSuccessful()) {
                log.error("Failed to recompute blast zones: HTTP {}",
                        response.getStatusCode());
            } else {
                log.info("Triggered recomputation of blast zones");
            }
        } catch (Exception ex) {
            log.error("Error while recomputing blast zones: {}", ex.getMessage());
            throw new RuntimeException("Failed to recompute blast zones", ex);
        }
    }

    private String createSingleMiAnalysisJob(MiAnalysisJobRequest request) {
        String url = BASE_URL+ MI_ANALYSIS_ENDPOINT + JOBS_ENDPOINT;

        try {
            HttpEntity<MiAnalysisJobRequest> entity = createHttpEntity(request);

            ResponseEntity<BasicResponse<MiAnalysisJob>> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    entity,
                    new ParameterizedTypeReference<BasicResponse<MiAnalysisJob>>() {}
            );

            return handleCreateJobResponse(response, request);

        } catch (HttpClientErrorException | HttpServerErrorException ex) {
            handleHttpError("create MI analysis job", request.getQuoteId(), ex);
        } catch (RestClientException ex) {
            handleRestClientError("create MI analysis job", request.getQuoteId(), ex);
        } catch (Exception ex) {
            handleGenericError("create MI analysis job", request.getQuoteId(), ex);
        }
        return null;
    }

    private void triggerSingleMiAnalysisJob(String jobId) {
        String url = BASE_URL+ MI_ANALYSIS_ENDPOINT + JOB_ENDPOINT + "/" + jobId;

        try {
            HttpEntity<Object> entity = createHttpEntity(null);

            ResponseEntity<BasicResponse<String>> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    entity,
                    new ParameterizedTypeReference<BasicResponse<String>>() {}
            );

            handleTriggerJobResponse(response, jobId);

        } catch (HttpClientErrorException | HttpServerErrorException ex) {
            handleHttpError("trigger MI analysis job", jobId, ex);
        } catch (RestClientException ex) {
            handleRestClientError("trigger MI analysis job", jobId, ex);
        } catch (Exception ex) {
            handleGenericError("trigger MI analysis job", jobId, ex);
        }
    }

    private <T> HttpEntity<T> createHttpEntity(T body) {
        HttpHeaders headers = new HttpHeaders();
        headers.set(CLIENT_ID, TenantContextHolder.getTenantId());
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(List.of(MediaType.APPLICATION_JSON));
        return new HttpEntity<>(body, headers);
    }

    private String handleCreateJobResponse(ResponseEntity<BasicResponse<MiAnalysisJob>> response,
                                         MiAnalysisJobRequest request) {
        if (!response.getStatusCode().is2xxSuccessful()) {
            log.error("Failed to create MI analysis job for quote {}: HTTP {}",
                    request.getQuoteId(), response.getStatusCode());
            return null;
        }

        BasicResponse<MiAnalysisJob> responseBody = response.getBody();
        if (responseBody == null) {
            log.error("Received null response body when creating MI analysis job for quote {}",
                    request.getQuoteId());
            return null;
        }

        MiAnalysisJob result = responseBody.getResult();
        if (result == null) {
            log.error("Received null result when creating MI analysis job for quote {}",
                    request.getQuoteId());
            return null;
        }

        log.info("Successfully created MI analysis job {} for quote {}",
                result.getJobId(), request.getQuoteId());
        return result.getJobId();
    }

    private void handleTriggerJobResponse(ResponseEntity<BasicResponse<String>> response, String jobId) {
        if (!response.getStatusCode().is2xxSuccessful()) {
            log.error("Failed to trigger MI analysis for job {}: HTTP {}",
                    jobId, response.getStatusCode());
            return;
        }

        BasicResponse<String> responseBody = response.getBody();
        if (responseBody == null) {
            log.error("Received null response body when triggering MI analysis for job {}", jobId);
            return;
        }

        String result = responseBody.getResult();
        if (result == null) {
            log.error("Received null result when triggering MI analysis for job {}", jobId);
            return;
        }

        log.info("Successfully triggered MI analysis for job {}", jobId);
    }

    private void handleHttpError(String operation, String identifier, HttpStatusCodeException ex) {
        String message = String.format("HTTP error while trying to %s for %s: %s - %s",
                operation, identifier, ex.getStatusCode(), ex.getResponseBodyAsString());
        log.error(message);
        sendSlackAlert(operation, identifier, ex.getStatusCode().toString());
    }

    private void handleRestClientError(String operation, String identifier, RestClientException ex) {
        String message = String.format("Network error while trying to %s for %s: %s",
                operation, identifier, ex.getMessage());
        log.error(message, ex);
        sendSlackAlert(operation, identifier, ex.getMessage());
    }

    private void handleGenericError(String operation, String identifier, Exception ex) {
        String message = String.format("Unexpected error while trying to %s for %s: %s",
                operation, identifier, ex.getMessage());
        log.error(message, ex);
        sendSlackAlert(operation, identifier, ex.getMessage());
    }

    private void sendSlackAlert(String operation, String identifier, String errorDetails) {
        String alertMessage = String.format("Failed to %s for %s: %s", operation, identifier, errorDetails);
        try {
            slackNotifier.sendAlert(alertMessage);
        } catch (Exception ex) {
            log.error("Failed to send Slack alert: {}", ex.getMessage());
        }
    }

    private void addDelayBetweenJobs() {
        try {
            Thread.sleep(JOB_TRIGGER_DELAY_MS);
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
            log.warn("Thread was interrupted during job delay");
        }
    }
}