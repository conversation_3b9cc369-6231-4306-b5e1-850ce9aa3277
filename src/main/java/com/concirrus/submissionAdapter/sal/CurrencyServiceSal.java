package com.concirrus.submissionAdapter.sal;


import com.concirrus.submissionAdapter.dto.currencyExchange.CurrencyRateApiResponse;
import com.concirrus.submissionAdapter.dto.currencyExchange.CurrencyRateRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.UUID;

@Component
@Slf4j
@RequiredArgsConstructor
public class CurrencyServiceSal {
    private static final String CURRENCY_SERVICE_BASE_URL = "http://currency-management-service.";
    public static final String PLATFORM_CURRENCY_MANAGEMENT = "/platform/currency-management";
    private final RestTemplate restTemplate;
    @Value("${namespace.currency-service}")
    private String currencyServiceNamespace ;
    private static final String RATES_ENDPOINT = "/rates";



    public CurrencyRateApiResponse getCurrencyRates(List<CurrencyRateRequest> requests,String clientId) {
        String baseUrl = CURRENCY_SERVICE_BASE_URL + currencyServiceNamespace+ PLATFORM_CURRENCY_MANAGEMENT + RATES_ENDPOINT;
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(List.of(MediaType.APPLICATION_JSON));
        headers.set("referenceId", clientId);
        headers.set("requestId", UUID.randomUUID().toString());

        HttpEntity<List<CurrencyRateRequest>> entity = new HttpEntity<>(requests, headers);

        ResponseEntity<CurrencyRateApiResponse> response = restTemplate.exchange(
                baseUrl,
                HttpMethod.POST,
                entity,
                CurrencyRateApiResponse.class
        );

        return response.getBody();
    }


}
