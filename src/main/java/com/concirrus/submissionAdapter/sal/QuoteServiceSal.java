package com.concirrus.submissionAdapter.sal;


import com.concirrus.submissionAdapter.dto.submission.Quote;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.concirrus.submissionAdapter.constants.Constants.FORWARD_SLASH;
import static com.concirrus.submissionAdapter.constants.Constants.QUOTE;

@Component
@Slf4j
public class QuoteServiceSal {

    private final RestTemplate restTemplate;
    private final AccessManagementSal accessManagementSal;
    private final String quoteServiceUrl;
    private final ObjectMapper objectMapper;

    public QuoteServiceSal(RestTemplate restTemplate, AccessManagementSal accessManagementSal, @Value("${service.rater-quote-service.url}") String quoteServiceUrl, ObjectMapper objectMapper) {
        this.restTemplate = restTemplate;
        this.accessManagementSal = accessManagementSal;
        this.quoteServiceUrl = quoteServiceUrl;
        this.objectMapper = objectMapper;
    }

    public Quote getQuoteById(String quoteId, String clientId){
        try {
            log.info("Fetching Quote for quote {} clientID {}..", quoteId, quoteId);

            String token = accessManagementSal.getToken(clientId);

            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", token);
            headers.set("client-id", clientId);

            log.info("CORESYSTEM :: Quote fetching endpoint {}", quoteServiceUrl+QUOTE+FORWARD_SLASH+quoteId );


            ResponseEntity<Object> responseEntity = restTemplate.exchange(
                    quoteServiceUrl+QUOTE+FORWARD_SLASH+quoteId,
                    HttpMethod.GET,
                    new HttpEntity<>(headers),
                    Object.class
            );

            Map<String, Object> responseBody = (Map<String, Object>) responseEntity.getBody();
            if (responseBody==null){
                log.info("Received NULL in response to QUOTE fetch request for quote {}",quoteId);
                return null;
            }
            return objectMapper.convertValue(responseBody, Quote.class);
        } catch (Exception e) {
            log.info("Exception {} occurred while fetching quote for quoteId {}, clientId {}", e.getMessage(), quoteId, clientId, e);
            return null;
        }
    }

    public List<Quote> getAllQuotesBySubmissionId(String submissionId, String clientId) {
        try {
            log.info("Fetching all quotes for submission {} clientID {}..", submissionId, clientId);

            String token = accessManagementSal.getToken(clientId);

            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", token);
            headers.set("client-id", clientId);

            // Build URL with query parameters similar to the curl request
            String url = UriComponentsBuilder.fromUriString(quoteServiceUrl + QUOTE)
                    .queryParam("sortBy", "createdAt")
                    .queryParam("sortOrder", "ASC")
                    .queryParam("pageNumber", 0)
                    .queryParam("pageSize", 100)
                    .queryParam("submissionId", submissionId)
                    .toUriString();


            ResponseEntity<Object> responseEntity = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    new HttpEntity<>(headers),
                    Object.class
            );

            Map<String, Object> responseBody = (Map<String, Object>) responseEntity.getBody();
            if (responseBody == null) {
                log.info("Received NULL in response to quotes fetch request for submission {}", submissionId);
                return new ArrayList<>();
            }

            List<Quote> quotes = new ArrayList<>();
            Object dataObj = responseBody.get("data");
            if (dataObj instanceof List<?> dataList) {
                dataList.stream()
                        .filter(Map.class::isInstance)
                        .map(item -> (Map<String, Object>) item)
                        .map(map -> objectMapper.convertValue(map, Quote.class))
                        .filter(Objects::nonNull)
                        .forEach(quotes::add);
            }
            return quotes;
        } catch (Exception e) {
            log.error("Exception {} occurred while fetching quotes for submissionId {}, clientId {}", e.getMessage(), submissionId, clientId, e);
            return new ArrayList<>();
        }
    }
}
