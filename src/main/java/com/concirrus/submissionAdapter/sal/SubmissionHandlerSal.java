package com.concirrus.submissionAdapter.sal;

import com.concirrus.submissionAdapter.dto.submission.BasicResponse;
import com.concirrus.submissionAdapter.dto.submission.InsuredAssetsResponse;
import com.concirrus.submissionAdapter.dto.submission.Submission;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

@Component
@Slf4j
public class SubmissionHandlerSal {

    private final AccessManagementSal accessManagementSal;
    private final RestTemplate restTemplate;
    private final String submissionNamespace;
    private final ObjectMapper objectMapper;
    private static final String baseUrl = "http://submission-handler-service.";
    private static final String getSubmissionByIdPath = "/submission-handler-service/api/v1/submissions/";
    private static final String getInsuredAssetsPath = "/submission-handler-service/api/v1/submissions/insured-assets/";
    private static final String AVIATION_WAR = "AVIATION_WAR";
    public SubmissionHandlerSal(AccessManagementSal accessManagementSal, RestTemplate restTemplate, @Value("${namespace.submission}") String submissionNamespace, ObjectMapper objectMapper) {
        this.accessManagementSal = accessManagementSal;
        this.restTemplate = restTemplate;
        this.submissionNamespace = submissionNamespace;
        this.objectMapper = objectMapper;
    }

    public Submission getSubmissionById(String submissionId, String clientId){
        String url =  baseUrl+ submissionNamespace + getSubmissionByIdPath  + submissionId ;
        try {
            log.info("Getting Submission {} ", submissionId);
            String token = accessManagementSal.getToken(clientId);
            HttpHeaders headers = new HttpHeaders();
            headers.set("client-id", clientId);
            headers.set("Authorization",token);

            ResponseEntity<BasicResponse> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    new HttpEntity<>(headers),
                    BasicResponse.class,
                    submissionId
            );

           if (response.getStatusCode() == HttpStatus.OK&&response.getBody()!=null){
               Object result = response.getBody().getResult();
               log.info("Successfully retrieved submission {}",submissionId);
               return  objectMapper.convertValue(result,Submission.class);
           }else {
               log.info("Failed to retrieved submission {}",submissionId);
               return null;
           }

        } catch (HttpClientErrorException | HttpServerErrorException ex) {
            log.error("Failed to fetch Submission {}: {} - {}", submissionId, ex.getStatusCode(), ex.getResponseBodyAsString());
            throw new RuntimeException("Failed to fetch Submission: " + ex.getStatusCode());
        } catch (RestClientException ex) {
            log.error("Error while calling Submission service for {}: {}", submissionId, ex.getMessage());
            throw new RuntimeException("Error while calling Submission service", ex);
        }
    }

    public InsuredAssetsResponse getInsuredAssetsForSubmission(String submissionId, String lob, Integer page, Integer size, String clientId) {
        // Default ProductType to AVIATION_WAR
        // Build URL
        String url = baseUrl+submissionNamespace + getInsuredAssetsPath+ AVIATION_WAR + "/" + submissionId;
        //String publicUrl = "https://dev-api.submission.concirrusquest.com/sub/submission-handler-service/api/v1/submissions/insured-assets/"+ AVIATION_WAR + "/" + submissionId;;

        try {
            // Build query parameters
            String token = accessManagementSal.getToken(clientId);
            UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromUriString(url)
                    .queryParam("lob", lob)
                    .queryParam("page", page)
                    .queryParam("size", size);

            HttpHeaders headers = new HttpHeaders();
            headers.set("client-id", clientId);
            headers.set("Authorization", token);
            // Make the GET request
            ResponseEntity<InsuredAssetsResponse> response = restTemplate.exchange(
                    uriBuilder.toUriString(),
                    HttpMethod.GET,
                    new HttpEntity<>(headers),
                    InsuredAssetsResponse.class
            );
            return response.getBody();
        } catch (Exception e) {
            log.info("Error calling getInsuredAssetsForSubmission for submissionId {}: {}", submissionId, e.getMessage());
            throw new RuntimeException("Failed to fetch insured assets", e);
        }
    }




}
