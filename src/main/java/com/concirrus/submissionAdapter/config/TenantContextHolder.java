package com.concirrus.submissionAdapter.config;


import org.springframework.util.Assert;

public class TenantContextHolder {
    private static final ThreadLocal<String> contextHolder = new ThreadLocal<>();
    private static final ThreadLocal<String> tenantAliasHolder = new ThreadLocal<>();

    public static void setTenantContext(String tenantId, String tenantAlias) {
        setTenantId(tenantId);
        setTenantAlias(tenantAlias);
    }

    public static void setTenantAlias(String tenantAlias) {
        Assert.notNull(tenantAlias, "TenantAlias cannot be null");
        tenantAliasHolder.set(tenantAlias);
    }

    public static String getTenantAlias() {
        return tenantAliasHolder.get();
    }

    public static void clearTenantAlias() {
        tenantAliasHolder.remove();
    }

    public static void setTenantId(String tenantId) {
        Assert.notNull(tenantId, "TenantId cannot be null");
        contextHolder.set(tenantId);
    }

    public static String getTenantId() {
        return contextHolder.get();
    }

    public static void clearTenantId() {
        contextHolder.remove();
    }

    public static void clear() {
        clearTenantId();
        clearTenantAlias();
    }
}

