package com.concirrus.submissionAdapter.config;

import com.mongodb.client.MongoCollection;
import net.javacrumbs.shedlock.core.LockProvider;
import net.javacrumbs.shedlock.provider.mongo.MongoLockProvider;
import org.bson.Document;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.MongoDatabaseFactory;

@Configuration
public class LockProviderConfig {

    @Bean
    public LockProvider lockProvider(MongoDatabaseFactory mongoDatabaseFactory) {
        MongoCollection<Document> collection = mongoDatabaseFactory
                .getMongoDatabase()
                .getCollection("blast_zone_shedlock");
        return new MongoLockProvider(collection);
    }
}