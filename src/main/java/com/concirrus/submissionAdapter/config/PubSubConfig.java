package com.concirrus.submissionAdapter.config;

import com.google.cloud.spring.pubsub.core.PubSubTemplate;
import com.google.cloud.spring.pubsub.integration.AckMode;
import com.google.cloud.spring.pubsub.integration.inbound.PubSubInboundChannelAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.messaging.MessageChannel;


@Configuration
@Slf4j
public class PubSubConfig {

    @Bean
    public DirectChannel submissionUpdateChannel() {return new DirectChannel();}

    @Bean
    public PubSubInboundChannelAdapter submissionUpdateChannelAdapter(MessageChannel submissionUpdateChannel, PubSubTemplate pubSubTemplate, @Value("${cloud.messaging.subscription.submission-update-queue}") String submissionUpdateSubscription) {
        PubSubInboundChannelAdapter adapter = new PubSubInboundChannelAdapter(pubSubTemplate, submissionUpdateSubscription);
        adapter.setOutputChannel(submissionUpdateChannel);
        adapter.setAckMode(AckMode.AUTO);
        return adapter;
    }

    @Bean
    public DirectChannel blastZoneCallbackChannel() { return new DirectChannel(); }

    @Bean
    public PubSubInboundChannelAdapter blastZoneCallbackChannelAdapter(
            MessageChannel blastZoneCallbackChannel,
            PubSubTemplate pubSubTemplate,
            @Value("${cloud.messaging.subscription.blastzone-callback-queue}") String blastZoneCallbackSubscription) {
        PubSubInboundChannelAdapter adapter = new PubSubInboundChannelAdapter(pubSubTemplate, blastZoneCallbackSubscription);
        adapter.setOutputChannel(blastZoneCallbackChannel);
        adapter.setAckMode(AckMode.AUTO);
        return adapter;
    }

}
