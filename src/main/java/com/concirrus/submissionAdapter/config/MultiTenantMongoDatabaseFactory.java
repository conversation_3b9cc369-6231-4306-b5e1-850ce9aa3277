package com.concirrus.submissionAdapter.config;

import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoDatabase;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.mongodb.core.SimpleMongoClientDatabaseFactory;

@Slf4j
public class MultiTenantMongoDatabaseFactory extends SimpleMongoClientDatabaseFactory {

    public static final String UNDER_SCORE = "_";
    private final MongoClient mongoClient;
    private static final String DEFAULT_DATABASE = "aggregation_db";

    public MultiTenantMongoDatabaseFactory(MongoClient mongoClient) {
        super(mongoClient, DEFAULT_DATABASE); // default database
        this.mongoClient = mongoClient;

    }

    @NotNull
    @Override
    public MongoDatabase getMongoDatabase() {
        String tenantAlias = TenantContextHolder.getTenantAlias();
        if (tenantAlias != null) {
            String databaseName = DEFAULT_DATABASE + UNDER_SCORE + tenantAlias;
            log.info("Using database: {}", databaseName);
            return mongoClient.getDatabase(databaseName);
        }
        return super.getMongoDatabase(); // fallback to default
    }
}