package com.concirrus.submissionAdapter.config;

import com.concirrus.submissionAdapter.utils.TenantIndexCreator;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoDatabase;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.SimpleMongoClientDatabaseFactory;
import org.springframework.data.mongodb.core.mapping.MongoMappingContext;

import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public class MultiTenantMongoDatabaseFactory extends SimpleMongoClientDatabaseFactory {

    public static final String UNDER_SCORE = "_";
    private final MongoClient mongoClient;
    private static final String DEFAULT_DATABASE = "aggregation_db";
    private final Set<String> initialized = ConcurrentHashMap.newKeySet();
    private final TenantIndexCreator tenantIndexCreator;
    public MultiTenantMongoDatabaseFactory(MongoClient mongoClient, TenantIndexCreator tenantIndexCreator) {
        super(mongoClient, DEFAULT_DATABASE); // default database
        this.mongoClient = mongoClient;
        this.tenantIndexCreator = tenantIndexCreator;
    }

    @NotNull
    @Override
    public MongoDatabase getMongoDatabase() {
        String tenantAlias = TenantContextHolder.getTenantAlias();
        String databaseName = (tenantAlias != null)
                ? DEFAULT_DATABASE + UNDER_SCORE + tenantAlias
                : DEFAULT_DATABASE;

        log.info("Using database: {}", databaseName);

        // Ensure indexes only once per DB
        if (initialized.add(databaseName)) {
            log.info("Ensuring indexes for database: {}", databaseName);
            MongoTemplate tenantTemplate = new MongoTemplate(mongoClient, databaseName);
            tenantIndexCreator.ensureIndexes(tenantTemplate);
        }

        return mongoClient.getDatabase(databaseName);
    }
}