package com.concirrus.submissionAdapter.config;

import com.concirrus.submissionAdapter.utils.TenantIndexCreator;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import com.mongodb.client.MongoDatabase;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.autoconfigure.mongo.MongoProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.mongodb.MongoDatabaseFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.mapping.MongoMappingContext;

import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

@Configuration
@Slf4j
public class MultiTenantMongoConfig {
    @Bean
    public TenantIndexCreator tenantIndexCreator(MongoMappingContext mappingContext) {
        return new TenantIndexCreator(mappingContext);
    }

    @Bean
    @ConfigurationProperties("spring.data.mongodb")
    @Primary
    public MongoProperties mongoProperties() {
        return new MongoProperties();
    }

    @Bean
    public MongoClient mongoClient(MongoProperties mongoProperties) {
        return MongoClients.create(mongoProperties.getUri());
    }

    @Bean
    @Primary
    public MongoDatabaseFactory mongoDatabaseFactory(MongoClient mongoClient, TenantIndexCreator tenantIndexCreator) {
        return new MultiTenantMongoDatabaseFactory(mongoClient, tenantIndexCreator);
    }

    @Bean
    @Primary
    public MongoTemplate mongoTemplate(MongoDatabaseFactory mongoDatabaseFactory) {
        return new MongoTemplate(mongoDatabaseFactory);
    }
}