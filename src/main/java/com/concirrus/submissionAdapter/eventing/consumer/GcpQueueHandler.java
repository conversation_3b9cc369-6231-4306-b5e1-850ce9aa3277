package com.concirrus.submissionAdapter.eventing.consumer;

import com.concirrus.submissionAdapter.config.TenantContextHolder;
import com.concirrus.submissionAdapter.dto.enums.ProductType;
import com.concirrus.submissionAdapter.dto.workflow.SubmissionUpdateEvent;
import com.concirrus.submissionAdapter.service.FactoryMap;
import com.concirrus.submissionAdapter.service.MessageHandlerFactory;
import com.concirrus.submissionAdapter.service.TenantService;
import com.concirrus.submissionAdapter.utils.SlackNotifier;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Objects;

@Component
@Slf4j
public class GcpQueueHandler {
    private final ObjectMapper objectMapper;
    private final FactoryMap factoryMap;
    private final SlackNotifier slackNotifier;
    private final TenantService tenantService;

    public GcpQueueHandler(ObjectMapper objectMapper, FactoryMap factoryMap, SlackNotifier slackNotifier, TenantService tenantService) {
        this.objectMapper = objectMapper;
        this.factoryMap = factoryMap;
        this.slackNotifier = slackNotifier;
        this.tenantService = tenantService;
    }

    @ServiceActivator(inputChannel = "submissionUpdateChannel")
    public void processSubmissionEvent(String message) {
        try {
            log.info("Received submission update event {}", message);

            SubmissionUpdateEvent updateEvent = objectMapper.readValue(message, SubmissionUpdateEvent.class);
            if (Objects.isNull(updateEvent.getClientId())){
                throw new RuntimeException("Client ID is null in submission update event  " + updateEvent.getSubmissionId() + " for update type " + updateEvent.getUpdateType());
            }
            if (!tenantService.isValidTenantId(updateEvent.getClientId())){
                log.info("Skipping submission update event for client id {} as it is not a valid tenant", updateEvent.getClientId());
                return;
            }
            setTenantContext(updateEvent);
            // Validate
            ProductType productType = parseProductType(updateEvent.getProductType());
            if (productType == null) {
                log.error("Invalid product type received: {}. Skipping event processing.", updateEvent.getProductType());
                throw new IllegalArgumentException("Invalid product type: " + updateEvent.getProductType());
            }

            MessageHandlerFactory factory = factoryMap.getFactory(productType);
            if (factory == null) {
                log.error("No factory found for product type: {}. Skipping event processing.", productType);
                return;
            }

            factory.processSubmissionUpdate(updateEvent);

        }catch (JsonProcessingException e){
            slackNotifier.sendAlert("Error occurred while parsing submission update event: " + message + " Error: " + e.getMessage());
            log.error("Error occurred while parsing submission update event: {} ...skipping event processing.", e.getMessage(), e);
        }catch (Exception e){
            slackNotifier.sendAlert("Error occurred while processing submission update event: " + e.getMessage());
            log.error("Error occurred while processing submission update event: {}", e.getMessage(), e);
            // alert for slack
        } finally {
            TenantContextHolder.clear();
        }
    }

    private ProductType parseProductType(String productTypeStr) {
        if (productTypeStr == null || productTypeStr.trim().isEmpty()) {
            log.error("Product type is null or empty");
            return null;
        }

        try {
            return ProductType.valueOf(productTypeStr.trim().toUpperCase());
        } catch (IllegalArgumentException e) {
            log.error("Invalid product type: {}. Valid values are: {}", productTypeStr,
                     java.util.Arrays.toString(ProductType.values()));
            return null;
        }
    }

    private void setTenantContext(SubmissionUpdateEvent updateEvent) {
        String clientId = updateEvent.getClientId();
        if (!StringUtils.hasText(clientId)){
            throw new RuntimeException("Client ID is null in submission update event  " + updateEvent.getSubmissionId() + " for update type " + updateEvent.getUpdateType());
        }
        String tenantAlias = tenantService.getTenantAlias(clientId);
        TenantContextHolder.setTenantContext(clientId, tenantAlias);
    }
}
