package com.concirrus.submissionAdapter.eventing.consumer;

import com.concirrus.submissionAdapter.config.TenantContextHolder;
import com.concirrus.submissionAdapter.dto.workflow.TaskCallBack;
import com.concirrus.submissionAdapter.service.TenantService;
import com.concirrus.submissionAdapter.service.politicalViolence.AggregationWorkflowService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * Consumer for handling BlastZone callback events from a message channel.
 */
@Component
@Slf4j
public class BlastZoneCallbackQueueHandler {

    private final ObjectMapper objectMapper;
    private final AggregationWorkflowService aggregationWorkflowService;
    private final TenantService tenantService;

    @Autowired
    public BlastZoneCallbackQueueHandler(ObjectMapper objectMapper, AggregationWorkflowService aggregationWorkflowService, TenantService tenantService) {
        this.objectMapper = objectMapper;
        this.aggregationWorkflowService = aggregationWorkflowService;
        this.tenantService = tenantService;
    }

    /**
     * Handles BlastZone callback events from the message channel.
     * @param message The callback event as a JSON string
     */
    @ServiceActivator(inputChannel = "blastZoneCallbackChannel")
    public void processBlastZoneCallbackEvent(String message) throws JsonProcessingException {
        try {
            log.info("Received BlastZone callback event: {}", message);
            TaskCallBack callback = objectMapper.readValue(message, TaskCallBack.class);

            setTenantContext(callback);
            aggregationWorkflowService.handleTaskCallback(callback);
        } catch (JsonProcessingException e) {
            log.error("Error parsing BlastZone callback event: {}", e.getMessage(), e);
        } catch (Exception e) {
            log.error("Error processing BlastZone callback event: {}", e.getMessage(), e);
        }finally {
            TenantContextHolder.clear();
        }
    }

    private void setTenantContext(TaskCallBack callback) {
        String clientId = callback.getClientId();
        if (!StringUtils.hasText(clientId)){
            throw new RuntimeException("Client ID is null in callback event");
        }
        String tenantAlias = tenantService.getTenantAlias(clientId);
        TenantContextHolder.setTenantContext(clientId, tenantAlias);

    }
}
