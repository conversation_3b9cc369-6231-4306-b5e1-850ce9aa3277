package com.concirrus.submissionAdapter.eventing.producer;

import com.concirrus.submissionAdapter.dto.workflow.LocationEventDTO;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.cloud.spring.pubsub.core.PubSubTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
@RequiredArgsConstructor
public class EventSender {

    private final PubSubTemplate pubSubTemplate;
    private final ObjectMapper objectMapper;

    @Value("${cloud.messaging.producer.blastzone-location-event}")
    private String blastzoneLocationTopic;



    public void sendBlastzoneLocationEvent(LocationEventDTO message) {
        sendMessage(message, blastzoneLocationTopic);
    }
    public void sendLocationEvents(List<LocationEventDTO> message) {
        if (message.isEmpty()) {
            log.info("No location events to send");
            return;
        }
        log.info("Sending {} location events", message.size());
        message.forEach(this::sendBlastzoneLocationEvent);

    }

    private void sendMessage(Object message, String topic) {
        try {
            String payload = objectMapper.writeValueAsString(message);
            log.info("Publishing to topic '{}': {}", topic, payload);
            pubSubTemplate.publish(topic, payload);
        } catch (Exception e) {
            log.error("Failed to send message to topic '{}'", topic, e);
            throw new RuntimeException("Error while sending message to Pub/Sub", e);
        }
    }
}
